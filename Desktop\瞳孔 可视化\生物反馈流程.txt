瞳孔生物反馈训练流程：
1. 刺激呈现流程：
①瞳孔大小调节方向提示语：“下面我们将进行瞳孔的放大/缩小训练”，呈现3s；（具体例如训练多少扩大，多少缩小可以设定）
②基线阶段：屏幕中间呈现“+”号，以及围绕“+”号的基准瞳孔大小虚线圈其大小不变，呈现7s；此阶段测得的瞳孔数据（特别是最后x秒的平均瞳孔大小）将被用作后续调节效果的基准线，用于计算瞳孔大小的变化
③自主调适阶段：基准虚线圈根据基线的瞳孔大小，确定大小；再出现一个实时代表瞳孔大小的实线圆圈，圆圈变小时线宽适度变宽，变大时线宽适度变窄，保持整个圆圈显示的总像素点个数恒定，这样屏幕的平均亮度不变；每32ms更新一次反馈（eyelink采样率设定的1000hz，按照30hz的瞳孔均值进行反馈，也就是33个值取均值作为瞳孔反馈值）；出现以下情况时不反馈，保持之前的值：a. 瞳孔大小小于1.5mm或者大于9mm；b.当出现异常变化速率:每秒变化超过0.0027mm/s. 这个阶段时长为15s（可后续调节）；
④反馈阶段：呈现最终15s 内有效调节瞳孔大小中，与预期调节方向一致的最大（放大条件）或者最小（缩小条件）值用于作为反馈，成功调节的话，反馈的圆圈保持绿色；失败的话，反馈圆圈为红色。时长为2s；

以上流程循环10次后，呈现一个休息界面，休息5s；

2. 训练顺序——放大或者缩小训练各循环3次（每次10次调节），可以由实验操作员输入先训练放大或者缩小。

3. 颜色设置：
①背景颜色：灰色（150 150 150）
②注视点：白色（255 255 255）
③基线瞳孔虚线圈颜色：绿色（其亮度值按照Y = 0.2126 R + 0.7152 G + 0. 0722 B)；保持与灰色亮度值一致；
④实时反馈瞳孔大小圆圈颜色：绿色，与基线颜色相同；
⑤最终反馈阶段圆圈颜色：正确方向保持绿色，不正确方向更改为红色（亮度同上）；



