# 瞳孔生物反馈训练程序改进说明

## 改进概述

根据您的要求，我完整阅读了`fixationWindow_fastSamples.py`程序，并参考其中所有与EyeLink连接相关的代码，对我们的瞳孔生物反馈训练程序进行了全面改进。

## 主要改进内容

### 1. EyeLink连接和初始化

**参考代码位置**: fixationWindow_fastSamples.py 第153-187行

**改进内容**:
- 采用标准的EyeLink连接流程
- 添加了完整的错误处理机制
- 使用`pylink.getEYELINK()`获取全局EyeLink对象
- 改进了EDF文件创建和头信息添加

**具体改进**:
```python
# 改进前
el_tracker = pylink.EyeLink(config.eyelink_ip)

# 改进后
if dummy_mode:
    el_tracker = pylink.EyeLink(None)
else:
    try:
        el_tracker = pylink.EyeLink(config.eyelink_ip)
    except RuntimeError as error:
        print('错误:', error)
        core.quit()
        sys.exit()
```

### 2. 图形环境和校准设置

**参考代码位置**: fixationWindow_fastSamples.py 第260-303行

**改进内容**:
- 使用`genv.getBackgroundColor()`和`genv.getForegroundColor()`
- 改进了屏幕清理和消息显示函数
- 添加了Retina显示器支持

**具体改进**:
```python
# 改进前
def clear_screen(win):
    win.flip()

# 改进后  
def clear_screen(win, genv):
    win.fillColor = genv.getBackgroundColor()
    win.flip()
```

### 3. 试次处理和错误管理

**参考代码位置**: fixationWindow_fastSamples.py 第401-721行

**改进内容**:
- 采用标准的试次开始/结束流程
- 添加了完整的键盘事件处理
- 改进了重新校准逻辑
- 使用标准的消息发送格式

**具体改进**:
```python
# 添加了标准的试次开始消息
el_tracker.sendMessage('TRIALID %d' % trial_index)
el_tracker.sendCommand("record_status_message '%s'" % status_msg)

# 改进了眼睛检测逻辑
eye_used = el_tracker.eyeAvailable()
if eye_used == 1:
    el_tracker.sendMessage("EYE_USED 1 RIGHT")
elif eye_used == 0 or eye_used == 2:
    el_tracker.sendMessage("EYE_USED 0 LEFT")
    eye_used = 0
```

### 4. 键盘事件处理

**参考代码位置**: fixationWindow_fastSamples.py 第564-584行

**改进内容**:
- 使用`keyboard.Keyboard()`对象进行键盘检测
- 添加了ESC键跳过试次功能
- 添加了Ctrl-C终止实验功能
- 改进了按键检测的可靠性

**具体改进**:
```python
# 改进前
keys = event.getKeys()
if 'escape' in keys:
    # 处理ESC键

# 改进后
keyPressList = kb.getKeys(keyList=None, waitRelease=False, clear=False)
if len(keyPressList) > 0:
    keyPressNamesList = [keyPress.name for keyPress in keyPressList]
    
    if 'escape' in keyPressNamesList:
        el_tracker.sendMessage('trial_skipped_by_user')
        abort_trial(win, genv)
        return pylink.SKIP_TRIAL
    
    if 'c' in keyPressNamesList and ('lctrl' in keyPressNamesList or 'rctrl' in keyPressNamesList):
        el_tracker.sendMessage('terminated_by_user')
        terminate_task(win, genv, None, None, None)
        return pylink.ABORT_EXPT
```

### 5. 数据记录和消息发送

**参考代码位置**: fixationWindow_fastSamples.py 第522-534, 631-651行

**改进内容**:
- 添加了Data Viewer兼容的消息格式
- 改进了试次变量记录
- 添加了阶段标记消息
- 使用标准的清屏命令

**具体改进**:
```python
# 添加了Data Viewer消息
el_tracker.sendMessage('!V CLEAR 128 128 128')
el_tracker.sendMessage('baseline_onset')
el_tracker.sendMessage('modulation_onset')
el_tracker.sendMessage('feedback_onset')

# 改进了试次变量记录
el_tracker.sendMessage('!V TRIAL_VAR condition %s' % condition)
el_tracker.sendMessage('!V TRIAL_VAR baseline_size %.3f' % baseline_size)
el_tracker.sendMessage('!V TRIAL_VAR success %s' % str(success))
```

### 6. 任务终止和数据传输

**参考代码位置**: fixationWindow_fastSamples.py 第332-377行

**改进内容**:
- 使用`pylink.getEYELINK()`获取全局对象
- 改进了EDF文件传输流程
- 添加了更好的错误处理
- 使用标准的清理流程

**具体改进**:
```python
# 改进前
def terminate_task(el_tracker, win, edf_file, session_folder, session_identifier):

# 改进后
def terminate_task(win, genv, edf_file, session_folder, session_identifier):
    el_tracker = pylink.getEYELINK()
    # 使用标准的终止流程
```

### 7. 试次中止处理

**参考代码位置**: fixationWindow_fastSamples.py 第380-398行

**改进内容**:
- 使用`pylink.getEYELINK()`获取全局对象
- 添加了标准的100ms延迟
- 改进了屏幕清理
- 使用标准的错误消息

### 8. 漂移校正和重新校准

**参考代码位置**: fixationWindow_fastSamples.py 第425-433行

**改进内容**:
- 添加了漂移校正功能
- 改进了重新校准逻辑
- 添加了配置选项控制
- 使用标准的校准流程

## 技术改进细节

### 消息发送标准化
- 所有阶段都有明确的开始/结束标记
- 使用Data Viewer兼容的消息格式
- 添加了时间戳和事件标记

### 错误处理增强
- 每个阶段都检查记录状态
- 添加了连接断开检测
- 改进了异常处理机制

### 键盘响应改进
- 使用更可靠的键盘检测方法
- 支持组合键检测（Ctrl-C）
- 添加了实时响应能力

### 数据质量保证
- 添加了眼睛可用性检查
- 改进了样本有效性验证
- 使用标准的数据过滤方法

## 兼容性改进

### PsychoPy兼容性
- 使用标准的PsychoPy窗口管理
- 改进了颜色和字体处理
- 添加了Retina显示器支持

### EyeLink兼容性
- 支持不同版本的EyeLink设备
- 使用标准的PyLink API调用
- 添加了版本检测和适配

## 配置灵活性

### 新增配置选项
```python
# 在config.py中添加了更多选项
self.drift_check_enabled = True    # 是否启用漂移校正
self.validation_enabled = True     # 是否启用验证
self.eyelink_ip = "*********"     # EyeLink IP地址
self.calibration_type = "HV9"      # 校准类型
```

## 程序稳定性提升

1. **连接稳定性**: 改进了EyeLink连接管理
2. **错误恢复**: 添加了自动错误恢复机制
3. **数据完整性**: 确保所有数据正确记录
4. **用户体验**: 改进了交互响应和错误提示

## 总结

通过参考`fixationWindow_fastSamples.py`的标准实现，我们的瞳孔生物反馈训练程序现在具有：

- ✅ 标准的EyeLink连接和配置流程
- ✅ 完整的错误处理和恢复机制
- ✅ 可靠的键盘事件处理
- ✅ Data Viewer兼容的数据记录
- ✅ 灵活的校准和漂移校正
- ✅ 稳定的试次管理流程

这些改进确保了程序的稳定性、可靠性和与EyeLink系统的完全兼容性。
