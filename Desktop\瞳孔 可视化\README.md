# EyeLink瞳孔直径实时显示程序

## 项目简介

这是一个用于EyeLink眼动仪的Python程序，可以实时获取并显示瞳孔直径数据。程序提供了直观的图形用户界面，能够实时显示左右眼的瞳孔直径变化，并绘制实时变化曲线图。

## 功能特性

### 核心功能
- **实时数据采集**: 从EyeLink眼动仪实时获取瞳孔直径数据
- **双眼监测**: 同时显示左眼和右眼的瞳孔直径
- **实时可视化**: 动态更新的瞳孔直径变化曲线图
- **用户友好界面**: 基于tkinter的图形用户界面

### 显示功能
- 实时数字显示当前瞳孔直径值（像素单位）
- 实时曲线图显示最近30秒的瞳孔直径变化
- 连接状态和记录状态指示
- 采样率显示

### 数据管理
- 自动管理数据缓存（最多保存1000个数据点）
- 异常数据过滤（过滤无效或异常的瞳孔数据）
- 线程安全的数据采集

## 系统要求

### 硬件要求
- EyeLink系列眼动仪（EyeLink 1000, EyeLink Portable DUO等）
- Windows/macOS/Linux操作系统
- 至少4GB RAM
- 网络连接（用于连接EyeLink主机）

### 软件要求
- Python 3.7或更高版本
- Conda包管理器
- EyeLink Developers Kit（必须先安装）

## 安装指南

### 1. 创建Conda环境
```bash
# 创建名为eyetracking的conda环境
conda create -n eyetracking python=3.9

# 激活环境
conda activate eyetracking
```

### 2. 安装EyeLink Developers Kit
在安装Python依赖之前，必须先安装EyeLink Developers Kit：

- **Windows**: 从SR Research官网下载并安装EyeLink Developers Kit
- **macOS**: 下载并安装macOS版本的Developers Kit
- **Linux**: 按照SR Research提供的Linux安装指南进行安装

### 3. 安装Python依赖
```bash
# 安装基础科学计算库
pip install numpy matplotlib scipy pandas

# 安装PyLink（EyeLink Python API）
pip install --index-url=https://pypi.sr-research.com sr-research-pylink
```

或者使用requirements.txt文件：
```bash
pip install -r requirements.txt
# 然后单独安装PyLink
pip install --index-url=https://pypi.sr-research.com sr-research-pylink
```

## 快速开始

### 方法一：自动设置（推荐）
1. **运行环境设置脚本**
   ```bash
   python setup_environment.py
   ```
   这将自动创建conda环境并安装所有依赖。

2. **运行演示版本**
   ```bash
   # Windows用户可以直接双击
   run_demo.bat

   # 或者手动运行
   conda activate eyetracking
   python pupil_display_demo.py
   ```

### 方法二：手动设置
1. **创建conda环境**
   ```bash
   conda create -n eyetracking python=3.9
   conda activate eyetracking
   ```

2. **安装依赖**
   ```bash
   pip install numpy matplotlib scipy pandas
   pip install --index-url=https://pypi.sr-research.com sr-research-pylink
   ```

## 使用方法

### 演示版本（无需硬件）
演示版本使用模拟数据，可以在没有EyeLink硬件的情况下测试程序功能：

```bash
conda activate eyetracking
python pupil_display_demo.py
```

**演示版本功能：**
- 模拟真实的瞳孔直径变化
- 包含呼吸影响、随机噪声、眨眼等现象
- 可调节模拟参数（基础瞳孔大小、噪声幅度等）
- 完整的GUI界面和实时图表

### 实际版本（需要EyeLink硬件）

#### 1. 启动程序
```bash
conda activate eyetracking
python pupil_realtime_display.py
```

#### 2. 连接EyeLink
1. 确保EyeLink眼动仪已开机并连接到网络
2. 点击"连接EyeLink"按钮
3. 程序将尝试连接到默认IP地址（*********）
4. 连接成功后，状态显示为"已连接"

#### 3. 开始数据采集
1. 点击"开始记录"按钮
2. 程序开始实时采集瞳孔直径数据
3. 观察实时数值显示和曲线图更新
4. 点击"停止记录"结束数据采集

#### 4. 数据解读
- **左眼/右眼瞳孔直径**: 以像素为单位显示当前瞳孔大小
- **曲线图**: 显示最近30秒的瞳孔直径变化趋势
- **蓝色线条**: 左眼数据
- **红色线条**: 右眼数据

## 程序架构

### 主要组件
1. **PupilDisplayApp类**: 主应用程序类
2. **GUI组件**: 基于tkinter的用户界面
3. **数据采集线程**: 独立线程进行实时数据采集
4. **可视化模块**: matplotlib实现的实时图表

### 数据流程
```
EyeLink眼动仪 → PyLink API → 数据采集线程 → 数据缓存 → GUI更新 → 实时显示
```

## 配置说明

### EyeLink设置
程序会自动配置以下EyeLink参数：
- 采样率: 1000Hz
- 数据类型: 包含瞳孔面积、注视点等信息
- 事件过滤: 注视、眼跳、眨眼等事件

### 可修改参数
在代码中可以调整的参数：
- `max_samples`: 最大数据点数量（默认1000）
- `EyeLink IP地址`: 默认为"*********"
- 图表显示时间窗口: 默认30秒
- 数据采集频率: 默认1ms间隔

## 故障排除

### 常见问题

**1. PyLink导入错误**
```
ImportError: No module named 'pylink'
```
解决方案：
- 确保已安装EyeLink Developers Kit
- 使用正确的pip命令安装PyLink
- 检查conda环境是否正确激活

**2. 连接失败**
```
无法连接到EyeLink眼动仪
```
解决方案：
- 检查EyeLink主机是否开机
- 确认网络连接正常
- 验证IP地址设置（默认*********）
- 检查防火墙设置

**3. 数据显示异常**
- 确保眼动仪校准正确
- 检查被试眼部是否被正确追踪
- 调整眼动仪摄像头位置和焦距

### 调试模式
程序包含详细的错误信息输出，运行时注意查看控制台输出以获取调试信息。

## 扩展功能

### 可能的改进方向
1. **数据保存**: 添加数据导出功能（CSV、Excel、HDF5格式）
2. **实时分析**: 添加瞳孔直径变化率、平均值等统计指标
3. **多被试支持**: 支持多个被试的数据管理
4. **自定义可视化**: 更多图表类型和显示选项
5. **报警功能**: 异常瞳孔变化的自动检测和报警

## 技术支持

### 联系方式
- EyeLink技术支持: <EMAIL>
- SR Research官方论坛: https://www.sr-research.com/support

### 相关资源
- [EyeLink Developers Kit文档](https://www.sr-research.com/support)
- [PyLink用户手册](https://www.sr-research.com/support/thread-195.html)
- [Python眼动追踪教程](https://github.com/zhiguo-eyelab/Pylink_book)

## 许可证

本程序仅供学术研究使用。使用前请确保已获得EyeLink眼动仪的合法使用授权。

## 版本历史

- **v1.0.0** (2025-07-02): 初始版本
  - 基本的实时瞳孔直径显示功能
  - GUI界面和实时图表
  - EyeLink连接和数据采集

---

**注意**: 使用本程序前请确保已正确安装和配置EyeLink Developers Kit，并且眼动仪硬件连接正常。
