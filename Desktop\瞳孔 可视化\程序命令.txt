1. 我使用的是eyelink的眼动仪。我需要写一个python程序，可以实时的从眼动仪中获取瞳孔直径的数值，然后实时在一个窗口中显示。这个项目的所有代码请在conda 的eyetracking环境中进行。你需要先上网查找eyelink python的api程序，然后写一段程序完成这个功能并且把程序的功能介绍在readme中写清楚
2. 现在修改瞳孔值是两个眼睛的平均值。并且让你的程序页面支持中文字体 在pupil realtime display中
3。 现在你需要改变一下显示内容：你需要虚拟的显示一个圆圈，它的直径和真正的瞳孔像素有一定的比例，并且这个比例可以在程序最显眼的地方设定。并且瞳孔数据需要取一段时间内的平均值，这个变量也要可以设定，先设定200ms。然后你要在程序中实时显示虚拟的圆圈变大变小。你需要看一下这个目录下的pdf论文中的圆圈，形状类似图片中的样子，虚线表示目标的瞳孔直径，这个值可以设定。实线表示现在实时的瞳孔直径，会随时变


待办：现在加入数据存储功能，你需要记录从开始记录到退出程序前的平均瞳孔值，事件发生（之后我会有实验流程，在不同的情况下显示不同的内容，需要把显示的内容的标签在合适的时间戳记录到）

阅读以下example中的官方txt内容。回答：如果我想要在记录结束后导出瞳孔数据，我应该如何调用api，写一个最简单的python，内容是在屏幕上播放纯黑屏2秒，然后是亮屏2秒，交替3次，同时要发送事件到主机记录，具体的api如何调用看pylink api userguide。运行结束后可以导出数据到data文件夹，并且文件命名根据当时的日期时间，我只需要edf文件就行。