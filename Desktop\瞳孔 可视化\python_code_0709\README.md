# 瞳孔生物反馈训练程序

## 程序简介

本程序基于PsychoPy和EyeLink眼动仪，实现实时瞳孔大小反馈训练。程序按照瞳孔生物反馈训练流程设计，包含基线测量、自主调适和反馈阶段。

## 文件说明

- `pupil_biofeedback_training.py` - 主程序文件
- `test_biofeedback.py` - 测试程序，用于验证基本功能
- `EyeLinkCoreGraphicsPsychoPy.py` - EyeLink图形环境支持文件
- `README.md` - 本说明文件

## 实验流程

### 1. 刺激呈现流程

每个试次包含以下阶段：

1. **指示语阶段** (3秒)
   - 显示"下面我们将进行瞳孔的放大/缩小训练"

2. **基线阶段** (7秒)
   - **阶段提示语** (2秒): "下面将进入基线阶段"
   - 屏幕中央显示"+"号和基准瞳孔大小虚线圈
   - 测量基线瞳孔大小，用作后续调节的参考

3. **自主调适阶段** (15秒)
   - **阶段提示语** (2秒): "下面将进入自主调适阶段\n请尝试放大/缩小瞳孔"
   - 显示基准虚线圈和实时反馈实线圆圈
   - 圆圈大小反映实时瞳孔大小
   - 圆圈变小时线宽变宽，变大时线宽变窄，保持总像素数恒定
   - 每32ms更新一次反馈 (30Hz)
   - 异常情况不反馈：瞳孔<1.5mm或>9mm，变化速率>0.0027mm/ms

4. **反馈阶段** (2秒)
   - **阶段提示语** (2秒): "下面将进入反馈阶段"
   - 显示调节期间的最佳表现
   - 成功：绿色圆圈，失败：红色圆圈

### 2. 训练顺序

- 放大或缩小训练各循环3次（每次10个试次）
- 可由实验操作员选择先训练放大或缩小
- 每10个试次后休息5秒

### 3. 颜色设置

- **背景颜色**: 灰色 (150, 150, 150)
- **注视点**: 白色 (255, 255, 255)
- **基线瞳孔虚线圈**: 绿色（亮度与灰色一致）
- **实时反馈圆圈**: 绿色（与基线颜色相同）
- **成功反馈**: 绿色
- **失败反馈**: 红色（亮度与绿色一致）

## 使用方法

### 1. 环境要求

- Python 3.7+
- PsychoPy
- PyLink (EyeLink SDK)
- NumPy
- PIL (Pillow)

### 2. 运行测试程序

首先运行测试程序验证基本功能：

```bash
python test_biofeedback.py
```

测试程序包含：
- 基本显示测试：验证视觉刺激显示
- EyeLink连接测试：验证眼动仪连接

### 3. 运行主程序

```bash
python pupil_biofeedback_training.py
```

程序启动后会要求输入：
- EDF文件名（8个字符以内）
- 训练顺序（先放大后缩小 或 先缩小后放大）
- 被试编号

### 4. 实验操作

1. **校准阶段**
   - 按照屏幕提示进行眼动仪校准
   - 确保校准质量良好

2. **实验阶段**
   - 被试按照指示语进行瞳孔控制训练
   - 注视屏幕中央的"+"号
   - 尝试控制瞳孔大小使反馈圆圈变化

3. **数据保存**
   - EDF文件自动保存到results文件夹
   - 包含完整的眼动和瞳孔数据

## 程序参数

可在程序开头的`ExperimentConfig`类中调整以下参数：

- `instruction_duration`: 指示语时长 (默认3秒)
- `baseline_duration`: 基线阶段时长 (默认7秒)
- `modulation_duration`: 自主调适阶段时长 (默认15秒)
- `feedback_duration`: 反馈阶段时长 (默认2秒)
- `rest_duration`: 休息时长 (默认5秒)
- `pupil_min_size`: 最小有效瞳孔大小 (默认1.5mm)
- `pupil_max_size`: 最大有效瞳孔大小 (默认9.0mm)
- `max_change_rate`: 最大变化速率 (默认0.0027mm/ms)
- `feedback_rate`: 反馈频率 (默认30Hz)
- `trials_per_block`: 每block试次数 (默认10)
- `blocks_per_condition`: 每条件block数 (默认3)
- `phase_prompt_duration`: 阶段提示语时长 (默认2秒)
- `show_phase_prompts`: 是否显示阶段提示语 (默认True)

## 数据输出

程序会在results文件夹中创建以下文件：
- EDF文件：包含完整的眼动和瞳孔数据
- 会话文件夹：以EDF文件名和时间戳命名

## 注意事项

1. **硬件要求**
   - 需要连接EyeLink眼动仪
   - 确保眼动仪正常工作并已校准

2. **软件设置**
   - 确保所有依赖库已正确安装
   - EyeLinkCoreGraphicsPsychoPy.py文件必须在同一目录

3. **实验环境**
   - 保持实验环境光照稳定
   - 被试头部位置稳定
   - 避免外界干扰

4. **数据质量**
   - 定期检查眼动仪校准质量
   - 监控瞳孔检测质量
   - 注意眨眼和注视偏移

## 故障排除

1. **导入错误**
   - 检查PyLink是否正确安装
   - 确保EyeLinkCoreGraphicsPsychoPy.py在正确位置

2. **连接错误**
   - 检查EyeLink主机IP地址 (默认*********)
   - 确认网络连接正常
   - 可使用dummy_mode=True进行调试

3. **显示问题**
   - 检查显示器设置
   - 调整全屏模式设置
   - 验证颜色显示正确

## 联系信息

如有问题请联系程序开发者或参考EyeLink官方文档。
