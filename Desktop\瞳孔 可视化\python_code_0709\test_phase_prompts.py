# -*- coding: utf-8 -*-
"""
阶段提示语功能测试程序
测试新添加的阶段提示语显示功能

作者: AI Assistant
日期: 2025-07-09
"""

from __future__ import division
from __future__ import print_function

import os
import sys
import time
from psychopy import visual, core, event, monitors, gui
from psychopy import logging

# 切换到脚本文件夹
script_path = os.path.dirname(sys.argv[0])
if len(script_path) != 0:
    os.chdir(script_path)

# 只显示关键日志信息
logging.console.setLevel(logging.CRITICAL)

def test_phase_prompts():
    """测试阶段提示语功能"""
    print("=" * 50)
    print("阶段提示语功能测试")
    print("=" * 50)
    
    # 导入配置
    try:
        from config import config
        print("✓ 配置文件导入成功")
    except ImportError:
        print("❌ 无法导入配置文件")
        return False
    
    # 创建窗口
    try:
        win = visual.Window(size=(800, 600),
                            fullscr=False,
                            units='pix',
                            color=config.bg_color)
        print("✓ PsychoPy窗口创建成功")
    except Exception as e:
        print(f"❌ 窗口创建失败: {e}")
        return False
    
    # 模拟图形环境对象
    class MockGenv:
        def getBackgroundColor(self):
            return config.bg_color
        
        def getForegroundColor(self):
            return [1, 1, 1]  # 白色前景
    
    genv = MockGenv()
    
    # 定义显示消息函数
    def show_message(win, genv, text, duration=None):
        msg = visual.TextStim(win, text,
                              color=genv.getForegroundColor(),
                              wrapWidth=config.text_wrap_width,
                              font=config.font_name,
                              height=config.font_size)
        win.fillColor = genv.getBackgroundColor()
        win.flip()
        msg.draw()
        win.flip()
        
        if duration:
            core.wait(duration)
        
        win.fillColor = genv.getBackgroundColor()
        win.flip()
    
    # 定义阶段提示语函数
    def show_phase_prompt(win, genv, prompt_text):
        if config.show_phase_prompts:
            show_message(win, genv, prompt_text, duration=config.phase_prompt_duration)
    
    # 显示测试开始信息
    start_msg = ("阶段提示语功能测试\n\n"
                "将依次显示各阶段的提示语\n"
                "每个提示语显示 {:.1f} 秒\n\n"
                "按空格键开始测试").format(config.phase_prompt_duration)
    
    show_message(win, genv, start_msg)
    event.waitKeys(keyList=['space'])
    
    # 测试各阶段提示语
    test_phases = [
        ("基线阶段提示语", config.baseline_prompt),
        ("自主调适阶段提示语 (放大)", config.modulation_prompt_enlarge),
        ("自主调适阶段提示语 (缩小)", config.modulation_prompt_shrink),
        ("反馈阶段提示语", config.feedback_prompt)
    ]
    
    for phase_name, prompt_text in test_phases:
        print(f"测试: {phase_name}")
        
        # 显示阶段名称
        phase_info = f"即将测试: {phase_name}\n\n按空格键继续"
        show_message(win, genv, phase_info)
        event.waitKeys(keyList=['space'])
        
        # 显示阶段提示语
        show_phase_prompt(win, genv, prompt_text)
        
        # 显示完成信息
        complete_msg = f"{phase_name} 测试完成\n\n按空格键继续下一个测试"
        show_message(win, genv, complete_msg)
        event.waitKeys(keyList=['space'])
    
    # 测试关闭提示语功能
    print("测试关闭提示语功能")
    original_setting = config.show_phase_prompts
    config.show_phase_prompts = False
    
    test_msg = "测试关闭提示语功能\n\n下面应该不会显示提示语\n\n按空格键继续"
    show_message(win, genv, test_msg)
    event.waitKeys(keyList=['space'])
    
    # 尝试显示提示语（应该被跳过）
    show_phase_prompt(win, genv, "这条提示语不应该显示")
    
    no_prompt_msg = "提示语已被跳过\n（如果您看到了其他提示语，说明功能有问题）\n\n按空格键继续"
    show_message(win, genv, no_prompt_msg)
    event.waitKeys(keyList=['space'])
    
    # 恢复原设置
    config.show_phase_prompts = original_setting
    
    # 测试自定义时长
    print("测试自定义提示语时长")
    original_duration = config.phase_prompt_duration
    config.phase_prompt_duration = 1.0  # 设置为1秒
    
    duration_test_msg = "测试自定义时长\n\n下一个提示语将显示1秒\n\n按空格键继续"
    show_message(win, genv, duration_test_msg)
    event.waitKeys(keyList=['space'])
    
    show_phase_prompt(win, genv, "这是1秒的提示语测试")
    
    # 恢复原设置
    config.phase_prompt_duration = original_duration
    
    # 测试完成
    final_msg = ("阶段提示语功能测试完成!\n\n"
                "测试项目:\n"
                "✓ 基线阶段提示语\n"
                "✓ 自主调适阶段提示语 (放大/缩小)\n"
                "✓ 反馈阶段提示语\n"
                "✓ 关闭提示语功能\n"
                "✓ 自定义显示时长\n\n"
                "按任意键退出")
    
    show_message(win, genv, final_msg)
    event.waitKeys()
    
    # 清理
    win.close()
    core.quit()
    
    print("✓ 阶段提示语功能测试完成")
    return True

def test_config_validation():
    """测试配置验证功能"""
    print("\n测试配置验证功能:")
    
    try:
        from config import config
        errors = config.validate_config()
        
        if errors:
            print("❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        else:
            print("✓ 配置验证通过")
            return True
    except Exception as e:
        print(f"❌ 配置验证出错: {e}")
        return False

def main():
    """主测试函数"""
    print("阶段提示语功能测试程序")
    print("=" * 50)
    
    # 选择测试项目
    dlg = gui.Dlg("测试选择")
    dlg.addText("请选择要进行的测试:")
    dlg.addField("测试项目:", choices=["阶段提示语显示测试", "配置验证测试", "全部测试"])
    
    ok_data = dlg.show()
    if not dlg.OK:
        print("用户取消测试")
        return
    
    test_choice = ok_data["测试项目:"]
    
    success = True
    
    if test_choice == "阶段提示语显示测试" or test_choice == "全部测试":
        success &= test_phase_prompts()
    
    if test_choice == "配置验证测试" or test_choice == "全部测试":
        success &= test_config_validation()
    
    if success:
        print("\n" + "=" * 50)
        print("✓ 所有测试通过!")
        print("阶段提示语功能工作正常。")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("❌ 部分测试失败!")
        print("请检查配置和代码。")
        print("=" * 50)

if __name__ == '__main__':
    main()
