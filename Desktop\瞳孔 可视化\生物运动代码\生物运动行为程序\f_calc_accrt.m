function [accs, rts] =  f_calc_accrt(datafile)
load(datafile);
% 提取数据
stimulus_type = results(:, 2); % 刺激类型
response = results(:, 3); % 按键反应（1表示正确，0表示错误）
reaction_time = results(:, 4); % 反应时间

% 对按键正确的反应时间进行分析
correct_reaction_time = reaction_time(response == 1);

% 剔除平均数上下三个标准差的极端值
mean_reaction_time = mean(correct_reaction_time);
std_reaction_time = std(correct_reaction_time);
lower_threshold = mean_reaction_time - 3 * std_reaction_time;
upper_threshold = mean_reaction_time + 3 * std_reaction_time;
filtered_reaction_time = correct_reaction_time(correct_reaction_time >= lower_threshold & correct_reaction_time <= upper_threshold);

% 计算结果
accs = sum(response == 1) / numel(response); % 总正确率
rts = mean(filtered_reaction_time); % 剔除极端值后的平均反应时
std_filtered_reaction_time = std(filtered_reaction_time); % 剔除极端值后的平均反应时的标准差

% 分析各刺激类型的数据
stimulus_types = unique(stimulus_type);
stimulus_accuracy = zeros(size(stimulus_types));
stimulus_mean_reaction_time = zeros(size(stimulus_types));
stimulus_std_reaction_time = zeros(size(stimulus_types));


for i = 1:numel(stimulus_types)
    type = stimulus_types(i);
    indices = stimulus_type == type & response == 1; % 仅考虑按键正确的数据
    stimulus_accuracy(i) = sum(response(indices) == 1) / sum(stimulus_type == type); % 刺激类型的正确率
    stimulus_reaction_time = reaction_time(indices); % 按键正确的反应时间
    mean_reaction_time = mean(stimulus_reaction_time); % 刺激类型的平均反应时
    std_reaction_time = std(stimulus_reaction_time); % 刺激类型的平均反应时的标准差
    
    % 剔除平均数上下三个标准差的极端值
    lower_threshold = mean_reaction_time - 3 * std_reaction_time;
    upper_threshold = mean_reaction_time + 3 * std_reaction_time;
    filtered_reaction_time = stimulus_reaction_time(stimulus_reaction_time >= lower_threshold & stimulus_reaction_time <= upper_threshold);
    
    % 更新结果
    stimulus_mean_reaction_time(i) = mean(filtered_reaction_time); % 剔除极端值后的平均反应时
    stimulus_std_reaction_time(i) = std(filtered_reaction_time); % 剔除极端值后的平均反应时的标准差
end


% 输出结果
disp(['总正确率：', num2str(accs)]);
disp(['平均反应时：', num2str(rts)]);
disp(['平均反应时的标准差：', num2str(std_filtered_reaction_time)]);
disp('各刺激类型的结果：');
for i = 1:numel(stimulus_types)
    disp(['刺激类型', num2str(stimulus_types(i)), '：']);
    disp(['正确率：', num2str(stimulus_accuracy(i))]);
    disp(['平均反应时：', num2str(stimulus_mean_reaction_time(i))]);
    disp(['平均反应时的标准差：', num2str(stimulus_std_reaction_time(i))]);
end
end