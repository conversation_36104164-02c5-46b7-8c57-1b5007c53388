@echo off
echo ================================================
echo 瞳孔生物反馈训练程序
echo ================================================
echo.
echo 请选择要运行的程序:
echo 1. 检查配置和依赖 (check_config.py)
echo 2. 运行基本功能测试 (test_biofeedback.py)
echo 3. 测试阶段提示语功能 (test_phase_prompts.py)
echo 4. 运行主实验程序 (pupil_biofeedback_training.py)
echo 5. 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" (
    echo.
    echo 正在检查配置和依赖...
    python check_config.py
    pause
) else if "%choice%"=="2" (
    echo.
    echo 正在启动基本功能测试...
    python test_biofeedback.py
    pause
) else if "%choice%"=="3" (
    echo.
    echo 正在测试阶段提示语功能...
    python test_phase_prompts.py
    pause
) else if "%choice%"=="4" (
    echo.
    echo 正在启动主实验程序...
    python pupil_biofeedback_training.py
    pause
) else if "%choice%"=="5" (
    echo 退出程序
    exit
) else (
    echo 无效选择，请重新运行
    pause
)
