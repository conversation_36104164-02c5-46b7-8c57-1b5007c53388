#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EyeLink瞳孔直径实时显示程序
Real-time Pupil Diameter Display for EyeLink Eye Tracker

作者: AI Assistant
日期: 2025-07-02
描述: 从EyeLink眼动仪实时获取瞳孔直径数据并在窗口中显示
"""

import sys
import time
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from collections import deque
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import matplotlib.font_manager as fm

# 配置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

try:
    import pylink
    PYLINK_AVAILABLE = True
except ImportError:
    PYLINK_AVAILABLE = False
    print("警告: PyLink未安装。请在conda eyetracking环境中安装pylink。")
    print("安装命令: pip install --index-url=https://pypi.sr-research.com sr-research-pylink")


class PupilDisplayApp:
    """瞳孔直径实时显示应用程序"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("EyeLink瞳孔直径实时显示")
        self.root.geometry("800x600")
        
        # 数据存储
        self.max_samples = 1000  # 最多显示1000个数据点
        self.left_pupil_data = deque(maxlen=self.max_samples)
        self.right_pupil_data = deque(maxlen=self.max_samples)
        self.average_pupil_data = deque(maxlen=self.max_samples)  # 平均瞳孔值
        self.time_data = deque(maxlen=self.max_samples)
        
        # EyeLink相关
        self.tracker = None
        self.is_connected = False
        self.is_recording = False
        self.data_thread = None
        self.stop_thread = False
        
        # 创建GUI
        self.create_widgets()
        
        # 初始化图表
        self.setup_plot()
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接控制框架
        control_frame = ttk.LabelFrame(main_frame, text="EyeLink控制", padding="5")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(control_frame, text="连接EyeLink", 
                                     command=self.connect_tracker)
        self.connect_btn.grid(row=0, column=0, padx=(0, 5))
        
        # 开始/停止记录按钮
        self.record_btn = ttk.Button(control_frame, text="开始记录", 
                                    command=self.toggle_recording, state="disabled")
        self.record_btn.grid(row=0, column=1, padx=(0, 5))
        
        # 状态标签
        self.status_label = ttk.Label(control_frame, text="状态: 未连接")
        self.status_label.grid(row=0, column=2, padx=(10, 0))
        
        # 数据显示框架
        data_frame = ttk.LabelFrame(main_frame, text="实时数据", padding="5")
        data_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 左眼瞳孔直径
        ttk.Label(data_frame, text="左眼瞳孔直径:").grid(row=0, column=0, sticky=tk.W)
        self.left_pupil_label = ttk.Label(data_frame, text="-- mm", font=("Arial", 12, "bold"))
        self.left_pupil_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 右眼瞳孔直径
        ttk.Label(data_frame, text="右眼瞳孔直径:").grid(row=1, column=0, sticky=tk.W)
        self.right_pupil_label = ttk.Label(data_frame, text="-- mm", font=("Arial", 12, "bold"))
        self.right_pupil_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))

        # 平均瞳孔直径
        ttk.Label(data_frame, text="平均瞳孔直径:").grid(row=2, column=0, sticky=tk.W)
        self.average_pupil_label = ttk.Label(data_frame, text="-- mm", font=("Arial", 12, "bold"), foreground="green")
        self.average_pupil_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))

        # 采样率
        ttk.Label(data_frame, text="采样率:").grid(row=3, column=0, sticky=tk.W)
        self.sample_rate_label = ttk.Label(data_frame, text="-- Hz")
        self.sample_rate_label.grid(row=3, column=1, sticky=tk.W, padx=(10, 0))
        
        # 图表框架
        self.plot_frame = ttk.LabelFrame(main_frame, text="瞳孔直径变化图", padding="5")
        self.plot_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
    def setup_plot(self):
        """设置matplotlib图表"""
        self.fig, self.ax = plt.subplots(figsize=(10, 4))
        self.ax.set_title("瞳孔直径实时变化", fontsize=14)
        self.ax.set_xlabel("时间 (秒)", fontsize=12)
        self.ax.set_ylabel("瞳孔直径 (像素)", fontsize=12)
        self.ax.grid(True, alpha=0.3)

        # 创建空的线条对象
        self.left_line, = self.ax.plot([], [], 'b-', label='左眼', linewidth=2)
        self.right_line, = self.ax.plot([], [], 'r-', label='右眼', linewidth=2)
        self.average_line, = self.ax.plot([], [], 'g-', label='平均值', linewidth=3, alpha=0.8)
        self.ax.legend(fontsize=10)
        
        # 嵌入到tkinter中
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        self.canvas = FigureCanvasTkAgg(self.fig, self.plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 设置动画
        self.ani = animation.FuncAnimation(self.fig, self.update_plot,
                                          interval=50, blit=False, cache_frame_data=False)
        
    def connect_tracker(self):
        """连接到EyeLink眼动仪"""
        if not PYLINK_AVAILABLE:
            messagebox.showerror("错误", "PyLink未安装。请先安装pylink库。")
            return
            
        try:
            # 初始化EyeLink连接
            self.tracker = pylink.EyeLink("*********")  # 默认EyeLink IP地址
            
            # 获取眼动仪信息
            eyelink_ver = self.tracker.getTrackerVersion()
            print(f"连接到EyeLink {eyelink_ver}")
            
            # 配置眼动仪
            self.configure_tracker()
            
            self.is_connected = True
            self.status_label.config(text="状态: 已连接")
            self.connect_btn.config(text="断开连接", command=self.disconnect_tracker)
            self.record_btn.config(state="normal")
            
            messagebox.showinfo("成功", "成功连接到EyeLink眼动仪！")
            
        except Exception as e:
            messagebox.showerror("连接错误", f"无法连接到EyeLink眼动仪:\n{str(e)}")
            print(f"连接错误: {e}")
            
    def configure_tracker(self):
        """配置EyeLink眼动仪设置"""
        if not self.tracker:
            return
            
        # 设置采样率
        self.tracker.sendCommand("sample_rate 1000")
        
        # 设置记录的数据类型
        self.tracker.sendCommand("file_event_filter = LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT")
        self.tracker.sendCommand("file_sample_data = LEFT,RIGHT,GAZE,HREF,AREA,GAZERES,STATUS,INPUT")
        self.tracker.sendCommand("link_event_filter = LEFT,RIGHT,FIXATION,SACCADE,BLINK,BUTTON,FIXUPDATE,INPUT")
        self.tracker.sendCommand("link_sample_data = LEFT,RIGHT,GAZE,GAZERES,AREA,STATUS,INPUT")
        
    def disconnect_tracker(self):
        """断开EyeLink连接"""
        if self.is_recording:
            self.stop_recording()
            
        if self.tracker:
            self.tracker.close()
            self.tracker = None
            
        self.is_connected = False
        self.status_label.config(text="状态: 未连接")
        self.connect_btn.config(text="连接EyeLink", command=self.connect_tracker)
        self.record_btn.config(state="disabled")
        
    def toggle_recording(self):
        """切换记录状态"""
        if self.is_recording:
            self.stop_recording()
        else:
            self.start_recording()
            
    def start_recording(self):
        """开始记录数据"""
        if not self.is_connected or not self.tracker:
            return
            
        try:
            # 开始记录
            self.tracker.startRecording(1, 1, 1, 1)
            pylink.pumpDelay(100)  # 等待记录开始
            
            self.is_recording = True
            self.stop_thread = False
            self.record_btn.config(text="停止记录")
            self.status_label.config(text="状态: 正在记录")
            
            # 启动数据采集线程
            self.data_thread = threading.Thread(target=self.data_collection_loop)
            self.data_thread.daemon = True
            self.data_thread.start()
            
        except Exception as e:
            messagebox.showerror("记录错误", f"无法开始记录:\n{str(e)}")
            
    def stop_recording(self):
        """停止记录数据"""
        if not self.is_recording:
            return
            
        self.stop_thread = True
        self.is_recording = False
        
        if self.tracker:
            self.tracker.stopRecording()
            
        self.record_btn.config(text="开始记录")
        self.status_label.config(text="状态: 已连接")
        
    def data_collection_loop(self):
        """数据采集循环（在单独线程中运行）"""
        start_time = time.time()
        
        while not self.stop_thread and self.is_recording:
            try:
                # 获取最新样本
                dt = self.tracker.getNewestSample()
                
                if dt is not None:
                    current_time = time.time() - start_time
                    
                    # 获取左眼数据
                    left_pupil = None
                    if dt.isLeftSample():
                        left_pupil = dt.getLeftEye().getPupilSize()
                        
                    # 获取右眼数据  
                    right_pupil = None
                    if dt.isRightSample():
                        right_pupil = dt.getRightEye().getPupilSize()
                    
                    # 更新数据
                    self.update_data(current_time, left_pupil, right_pupil)
                    
                time.sleep(0.001)  # 1ms延迟
                
            except Exception as e:
                print(f"数据采集错误: {e}")
                break
                
    def update_data(self, timestamp, left_pupil, right_pupil):
        """更新数据显示"""
        self.time_data.append(timestamp)

        # 处理瞳孔数据
        left_valid = left_pupil if left_pupil and left_pupil > 0 else np.nan
        right_valid = right_pupil if right_pupil and right_pupil > 0 else np.nan

        self.left_pupil_data.append(left_valid)
        self.right_pupil_data.append(right_valid)

        # 计算平均瞳孔值
        average_pupil = np.nan
        if not np.isnan(left_valid) and not np.isnan(right_valid):
            average_pupil = (left_valid + right_valid) / 2
        elif not np.isnan(left_valid):
            average_pupil = left_valid
        elif not np.isnan(right_valid):
            average_pupil = right_valid

        self.average_pupil_data.append(average_pupil)

        # 更新GUI标签（在主线程中）
        self.root.after(0, self.update_labels, left_pupil, right_pupil, average_pupil)
        
    def update_labels(self, left_pupil, right_pupil, average_pupil):
        """更新GUI标签显示"""
        left_text = f"{left_pupil:.1f} 像素" if left_pupil and left_pupil > 0 else "-- 像素"
        right_text = f"{right_pupil:.1f} 像素" if right_pupil and right_pupil > 0 else "-- 像素"
        average_text = f"{average_pupil:.1f} 像素" if not np.isnan(average_pupil) else "-- 像素"

        self.left_pupil_label.config(text=left_text)
        self.right_pupil_label.config(text=right_text)
        self.average_pupil_label.config(text=average_text)
        
    def update_plot(self, frame):
        """更新图表显示"""
        if len(self.time_data) < 2:
            return self.left_line, self.right_line, self.average_line

        # 转换为numpy数组
        times = np.array(self.time_data)
        left_data = np.array(self.left_pupil_data)
        right_data = np.array(self.right_pupil_data)
        average_data = np.array(self.average_pupil_data)

        # 更新线条数据
        self.left_line.set_data(times, left_data)
        self.right_line.set_data(times, right_data)
        self.average_line.set_data(times, average_data)

        # 自动调整坐标轴
        if len(times) > 0:
            self.ax.set_xlim(max(0, times[-1] - 30), times[-1] + 1)  # 显示最近30秒

            # 计算y轴范围（包括平均值数据）
            valid_data = np.concatenate([left_data[~np.isnan(left_data)],
                                       right_data[~np.isnan(right_data)],
                                       average_data[~np.isnan(average_data)]])
            if len(valid_data) > 0:
                y_min, y_max = np.min(valid_data), np.max(valid_data)
                y_range = y_max - y_min
                if y_range > 0:
                    self.ax.set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)
                else:
                    # 如果数据范围为0，设置一个默认范围
                    self.ax.set_ylim(y_min - 10, y_max + 10)

        return self.left_line, self.right_line, self.average_line
        
    def on_closing(self):
        """程序关闭时的清理工作"""
        if self.is_recording:
            self.stop_recording()
        if self.is_connected:
            self.disconnect_tracker()
        self.root.destroy()
        
    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


def main():
    """主函数"""
    print("EyeLink瞳孔直径实时显示程序")
    print("=" * 40)
    
    if not PYLINK_AVAILABLE:
        print("错误: PyLink未安装")
        print("请在conda eyetracking环境中运行以下命令安装:")
        print("pip install --index-url=https://pypi.sr-research.com sr-research-pylink")
        return
        
    app = PupilDisplayApp()
    app.run()


if __name__ == "__main__":
    main()
