# 瞳孔生物反馈训练程序使用指南

## 快速开始

### 1. 检查环境
双击运行 `run_experiment.bat`，选择选项1进行配置检查：
```
1. 检查配置和依赖 (check_config.py)
```

### 2. 测试程序
选择选项2运行测试程序：
```
2. 运行测试程序 (test_biofeedback.py)
```

### 3. 运行实验
选择选项3运行主实验程序：
```
3. 运行主实验程序 (pupil_biofeedback_training.py)
```

## 详细步骤

### 步骤1: 环境准备

1. **硬件准备**
   - 确保EyeLink眼动仪已连接
   - 检查网络连接 (默认IP: *********)
   - 调整被试座椅和头托位置

2. **软件检查**
   - 运行 `check_config.py` 检查所有依赖
   - 确认所有必要文件存在
   - 验证配置参数正确

### 步骤2: 参数配置

编辑 `config.py` 文件调整实验参数：

```python
# 基本设置
self.dummy_mode = False        # 设为True进行调试
self.full_screen = True        # 是否全屏
self.eyelink_ip = "*********" # EyeLink IP地址

# 时间设置 (秒)
self.instruction_duration = 3.0   # 指示语时长
self.baseline_duration = 7.0      # 基线阶段时长
self.modulation_duration = 15.0   # 自主调适阶段时长
self.feedback_duration = 2.0      # 反馈阶段时长

# 实验流程
self.trials_per_block = 10        # 每block试次数
self.blocks_per_condition = 3     # 每条件block数

# 阶段提示语设置
self.phase_prompt_duration = 2.0   # 阶段提示语时长
self.show_phase_prompts = True     # 是否显示阶段提示语
self.baseline_prompt = "下面将进入基线阶段"
self.modulation_prompt_enlarge = "下面将进入自主调适阶段\n请尝试放大瞳孔"
self.modulation_prompt_shrink = "下面将进入自主调适阶段\n请尝试缩小瞳孔"
self.feedback_prompt = "下面将进入反馈阶段"
```

### 步骤3: 测试运行

运行 `test_biofeedback.py` 进行基本功能测试：
- 测试视觉刺激显示
- 验证颜色设置
- 检查圆圈大小变化
- 测试成功/失败反馈

### 步骤4: 正式实验

1. **启动程序**
   ```
   python pupil_biofeedback_training.py
   ```

2. **输入实验信息**
   - EDF文件名 (8个字符以内)
   - 训练顺序 (先放大后缩小 或 先缩小后放大)
   - 被试编号

3. **校准眼动仪**
   - 按照屏幕提示进行校准
   - 确保校准质量良好
   - 必要时重新校准

4. **开始实验**
   - 被试按照指示语进行训练
   - 注视屏幕中央的"+"号
   - 尝试控制瞳孔大小

## 实验流程说明

### 单个试次流程

1. **指示语阶段 (3秒)**
   - 显示训练方向 (放大/缩小)

2. **基线阶段 (7秒)**
   - **阶段提示** (2秒): "下面将进入基线阶段"
   - 显示注视点"+"和基准虚线圈
   - 测量基线瞳孔大小

3. **自主调适阶段 (15秒)**
   - **阶段提示** (2秒): "下面将进入自主调适阶段\n请尝试放大/缩小瞳孔"
   - 显示基准虚线圈和实时反馈实线圈
   - 实线圈大小反映瞳孔大小
   - 30Hz更新频率

4. **反馈阶段 (2秒)**
   - **阶段提示** (2秒): "下面将进入反馈阶段"
   - 显示最佳表现
   - 绿色=成功，红色=失败

### 完整实验流程

- **放大训练**: 3个blocks，每个10个试次
- **缩小训练**: 3个blocks，每个10个试次
- **总计**: 60个试次
- **休息**: 每10个试次后休息5秒

## 数据输出

### 文件位置
```
results/
└── [EDF文件名]_[时间戳]/
    └── [EDF文件名]_[时间戳].EDF
```

### 数据内容
- 完整的眼动数据
- 瞳孔大小数据
- 实验事件标记
- 试次结果信息

## 常见问题

### Q1: 程序无法启动
**A**: 检查依赖库是否安装完整，运行 `check_config.py` 进行诊断

### Q2: EyeLink连接失败
**A**: 
- 检查网络连接
- 确认IP地址正确 (默认*********)
- 可设置 `dummy_mode = True` 进行调试

### Q3: 校准质量不佳
**A**:
- 调整被试位置
- 检查光照条件
- 清洁眼动仪镜头
- 重新进行校准

### Q4: 瞳孔检测异常
**A**:
- 检查瞳孔大小范围设置 (1.5-9.0mm)
- 调整变化速率阈值 (0.0027mm/ms)
- 确保被试眼睛状态良好

### Q5: 反馈圆圈不变化
**A**:
- 检查瞳孔数据是否有效
- 验证反馈频率设置 (30Hz)
- 确认样本缓冲区正常工作

## 自定义阶段提示语

### 基本设置

在 `config.py` 中可以自定义每个阶段的提示语：

```python
# 是否显示阶段提示语
self.show_phase_prompts = True

# 提示语显示时长（秒）
self.phase_prompt_duration = 2.0

# 各阶段提示语文本
self.baseline_prompt = "下面将进入基线阶段"
self.modulation_prompt_enlarge = "下面将进入自主调适阶段\n请尝试放大瞳孔"
self.modulation_prompt_shrink = "下面将进入自主调适阶段\n请尝试缩小瞳孔"
self.feedback_prompt = "下面将进入反馈阶段"
```

### 自定义示例

**简短提示语**:
```python
self.phase_prompt_duration = 1.5
self.baseline_prompt = "基线测量"
self.modulation_prompt_enlarge = "放大瞳孔"
self.modulation_prompt_shrink = "缩小瞳孔"
self.feedback_prompt = "查看结果"
```

**详细提示语**:
```python
self.phase_prompt_duration = 4.0
self.baseline_prompt = "基线测量阶段\n\n请保持自然状态\n注视屏幕中央"
self.modulation_prompt_enlarge = "瞳孔放大训练\n\n请想象明亮的场景\n或回忆兴奋的时刻"
self.modulation_prompt_shrink = "瞳孔缩小训练\n\n请想象昏暗的环境\n保持平静放松"
self.feedback_prompt = "表现反馈\n\n即将显示您的训练结果"
```

**关闭提示语**:
```python
self.show_phase_prompts = False  # 不显示任何阶段提示语
```

### 使用建议

1. **提示语时长**: 建议1-5秒，太短看不清，太长影响节奏
2. **文本内容**: 根据被试群体调整详细程度
3. **换行格式**: 使用 `\n` 进行换行，提高可读性
4. **语言风格**: 保持简洁明了，避免过于复杂的表述
5. **个性化**: 可根据不同实验条件设置不同的提示语

## 参数调整建议

### 时间参数
- **基线时长**: 建议7-10秒，确保稳定测量
- **调适时长**: 建议10-20秒，给予充分练习时间
- **反馈时长**: 建议2-3秒，确保被试看清结果
- **提示语时长**: 建议1-5秒，平衡信息传达和实验节奏

### 瞳孔参数
- **大小范围**: 根据被试群体调整 (成人通常2-8mm)
- **变化速率**: 过严会丢失数据，过松会包含噪声
- **反馈频率**: 30Hz平衡了实时性和稳定性

### 实验设计
- **试次数量**: 每条件至少20-30个试次
- **休息间隔**: 避免疲劳，保持数据质量
- **训练顺序**: 可随机化或平衡设计
- **提示语策略**: 根据被试理解能力调整详细程度

## 技术支持

如遇到技术问题，请：
1. 查看程序输出的错误信息
2. 检查EyeLink官方文档
3. 验证PsychoPy版本兼容性
4. 联系技术支持人员

## 注意事项

1. **实验环境**: 保持光照稳定，避免干扰
2. **被试状态**: 确保被试理解任务，状态良好
3. **数据备份**: 及时备份EDF文件
4. **质量监控**: 实时监控数据质量
5. **伦理规范**: 遵守实验伦理要求
