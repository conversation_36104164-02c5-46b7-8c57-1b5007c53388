# -*- coding: utf-8 -*-
"""
本模块提供了一个函数 `run_pupil_range_task`，用于执行一个简单的视觉刺激任务
来校准用户的瞳孔反应范围。

它会显示交替的黑白全屏刺激，并实时记录瞳孔大小，最终返回在两种条件下
瞳孔大小的最小值和最大值。
"""

import pylink
import os
import sys
import tkinter as tk
from datetime import datetime
import threading
import time

# ##############################################################################
#                             实验参数设置
# ##############################################################################

# 定义颜色
BLACK = "black"
WHITE = "white"

# 试次重复次数
N_REPETITIONS = 3

# 黑/白屏显示时间 (毫秒) - 可调参数
STIMULUS_DURATION_MS = 2000

# 设置数据文件夹
DATA_DIR = 'data'
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

# 全局变量用于存储瞳孔数据和线程控制
pupil_data_lock = threading.Lock()
min_pupil_black = float('inf')
max_pupil_white = 0.0
is_running = True
current_screen_color = None # 'black' or 'white'

def pupil_data_collection_thread(el_tracker):
    """
    一个在后台运行的线程，用于持续从眼动仪获取数据。
    """
    global min_pupil_black, max_pupil_white, is_running, current_screen_color

    while is_running:
        sample = el_tracker.getNewestSample()
        if sample is not None:
            left_pupil = 0
            right_pupil = 0
            
            # 获取瞳孔数据
            if sample.isLeftSample():
                # getPupilSize() 可能会返回 0.0，代表数据丢失
                pupil = sample.getLeftEye().getPupilSize()
                if pupil > 0:
                    left_pupil = pupil
            if sample.isRightSample():
                pupil = sample.getRightEye().getPupilSize()
                if pupil > 0:
                    right_pupil = pupil
            
            # 计算平均瞳孔值，确保只使用有效的非零值
            valid_pupils = []
            if left_pupil > 0:
                valid_pupils.append(left_pupil)
            if right_pupil > 0:
                valid_pupils.append(right_pupil)

            if not valid_pupils:
                continue
            
            avg_pupil = sum(valid_pupils) / len(valid_pupils)

            # 根据当前屏幕颜色更新最大/最小值
            with pupil_data_lock:
                if current_screen_color == 'black':
                    if avg_pupil < min_pupil_black:
                        min_pupil_black = avg_pupil
                elif current_screen_color == 'white':
                    if avg_pupil > max_pupil_white:
                        max_pupil_white = avg_pupil
        # 稍微暂停，避免CPU占用过高
        time.sleep(0.001)

def run_pupil_range_task(el_tracker, parent_window):
    """
    执行瞳孔范围查找任务。

    :param el_tracker: 一个已经连接并配置好的pylink.EyeLink实例。
    :param parent_window: 调用此函数的父tkinter窗口。
    :return: 一个元组 (min_pupil, max_pupil)。如果未收集到有效数据，
             min_pupil将是float('inf'), max_pupil将是0.0。
    """
    global is_running, current_screen_color, min_pupil_black, max_pupil_white
    
    # 重置全局变量
    min_pupil_black = float('inf')
    max_pupil_white = 0.0
    is_running = True
    current_screen_color = None

    # 创建一个Toplevel窗口
    task_window = tk.Toplevel(parent_window)
    
    # **修复方法**: 先设置窗口几何尺寸，再设为全屏
    screen_width = parent_window.winfo_screenwidth()
    screen_height = parent_window.winfo_screenheight()
    task_window.geometry(f"{screen_width}x{screen_height}+0+0")
    task_window.attributes('-fullscreen', True)

    task_window.configure(bg=BLACK)
    task_window.config(cursor="none")

    # 确保此窗口在最上层，并捕获所有输入
    task_window.transient(parent_window)
    task_window.grab_set()

    # 开始记录
    el_tracker.startRecording(1, 1, 1, 1)
    pylink.msecDelay(100)

    # 启动数据采集线程
    data_thread = threading.Thread(target=pupil_data_collection_thread, args=(el_tracker,))
    data_thread.start()

    def finish_task():
        global is_running
        is_running = False
        data_thread.join()

        el_tracker.stopRecording()
        
        # 显示最终结果
        task_window.configure(bg='grey')
        min_val_str = f"{min_pupil_black:.2f}" if min_pupil_black != float('inf') else "无有效数据"
        max_val_str = f"{max_pupil_white:.2f}" if max_pupil_white != 0.0 else "无有效数据"
        result_text = (f"瞳孔范围测试完成!\n\n"
                       f"黑屏最小瞳孔值: {min_val_str}\n"
                       f"白屏最大瞳孔值: {max_val_str}\n\n"
                       f"窗口将在3秒后关闭。")
        label = tk.Label(task_window, text=result_text, font=("Arial", 24), bg='grey', fg='white')
        label.pack(expand=True)
        task_window.update()
        
        task_window.after(3000, task_window.destroy)

    def run_trial(trial_num):
        global current_screen_color
        if trial_num > N_REPETITIONS:
            finish_task()
            return
            
        with pupil_data_lock:
            current_screen_color = 'black'
        task_window.configure(bg=BLACK)
        task_window.update()
        el_tracker.sendMessage(f"RANGE_CAL_TRIAL_{trial_num}_BLACK_ON")
        
        task_window.after(STIMULUS_DURATION_MS, lambda: switch_to_white(trial_num))

    def switch_to_white(trial_num):
        global current_screen_color
        with pupil_data_lock:
            current_screen_color = 'white'
        task_window.configure(bg=WHITE)
        task_window.update()
        el_tracker.sendMessage(f"RANGE_CAL_TRIAL_{trial_num}_WHITE_ON")
        
        task_window.after(STIMULUS_DURATION_MS, lambda: run_trial(trial_num + 1))

    # 开始第一个试次
    run_trial(1)
    
    # 等待Toplevel窗口被销毁
    parent_window.wait_window(task_window)
    
    return (min_pupil_black, max_pupil_white)


# -----------------------------------------------------------------------------
# 以下部分是用于独立运行此脚本进行测试的，在被导入时不会执行
# -----------------------------------------------------------------------------
def run_experiment_standalone():
    """
    用于独立测试此脚本的主实验函数
    """
    # 设置是否以虚拟模式运行（不连接真实眼动仪，用于调试）
    DUMMY_MODE = False

    el_tracker = None
    try:
        if DUMMY_MODE:
            el_tracker = pylink.EyeLink(None)
        else:
            el_tracker = pylink.EyeLink("*********")
        print("Eyelink 连接成功.")
    except RuntimeError as error:
        print(f"Eyelink 连接失败: {error}")
        return

    edf_filename_host = "TEST.EDF"
    try:
        el_tracker.openDataFile(edf_filename_host)
    except RuntimeError as e:
        print(f"无法打开EDF文件: {e}")
        el_tracker.close()
        return
    
    el_tracker.setOfflineMode()
    file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,AREA,PUPIL,GAZERES,STATUS,INPUT'
    link_sample_flags = 'LEFT,RIGHT,GAZE,AREA,PUPIL,GAZERES,STATUS,INPUT'
    el_tracker.sendCommand(f"file_sample_data = {file_sample_flags}")
    el_tracker.sendCommand(f"link_sample_data = {link_sample_flags}")
    
    # 在独立测试中，我们需要一个虚拟的屏幕尺寸来传递
    screen_width = 1920
    screen_height = 1080
    el_tracker.sendCommand(f"screen_pixel_coords = 0 0 {screen_width - 1} {screen_height - 1}")
    el_tracker.sendMessage(f"DISPLAY_COORDS 0 0 {screen_width - 1} {screen_height - 1}")

    root = tk.Tk()
    root.withdraw() # 隐藏主窗口

    min_val, max_val = run_pupil_range_task(el_tracker, root)
    
    print(f"任务结束. Min: {min_val}, Max: {max_val}")

    # 清理
    el_tracker.setOfflineMode()
    pylink.msecDelay(500)
    el_tracker.closeDataFile()
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    local_edf_filename = f"pupil_{timestamp}.edf"
    local_edf_path = os.path.join(DATA_DIR, local_edf_filename)
    
    try:
        el_tracker.receiveDataFile(edf_filename_host, local_edf_path)
    except RuntimeError as error:
        print(f"文件传输失败: {error}")

    el_tracker.close()
    root.destroy()

if __name__ == '__main__':
    # 注意: 如果独立运行，请确保没有其他tkinter mainloop在跑
    # run_experiment_standalone()
    pass 