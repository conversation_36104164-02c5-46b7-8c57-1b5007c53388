# -*- coding: utf-8 -*-
"""
瞳孔生物反馈训练程序 - 测试版本
简化版本用于测试基本功能

作者: AI Assistant
日期: 2025-07-09
"""

from __future__ import division
from __future__ import print_function

import pylink
import os
import time
import sys
import numpy as np
from psychopy import visual, core, event, monitors, gui
from math import pi

# 切换到脚本文件夹
script_path = os.path.dirname(sys.argv[0])
if len(script_path) != 0:
    os.chdir(script_path)

# 只显示关键日志信息
from psychopy import logging
logging.console.setLevel(logging.CRITICAL)

def test_basic_setup():
    """测试基本设置"""
    print("测试基本PsychoPy设置...")
    
    # 创建窗口
    win = visual.Window(size=(800, 600),
                        fullscr=False,
                        units='pix',
                        color=[150/255*2-1, 150/255*2-1, 150/255*2-1])  # 灰色背景
    
    # 创建刺激
    fixation = visual.TextStim(win, '+', height=50, color=[1, 1, 1], pos=(0, 0))
    
    # 计算绿色亮度值与灰色一致
    gray_luminance = 150  # 灰色亮度
    green_g = gray_luminance / 0.7152  # 约209.8
    green_color = [0, green_g/255*2-1, 0]
    
    baseline_circle = visual.Circle(win,
                                   radius=50,
                                   lineWidth=3,
                                   lineColor=green_color,
                                   fillColor=None,
                                   pos=(0, 0))
    
    feedback_circle = visual.Circle(win,
                                   radius=50,
                                   lineWidth=3,
                                   lineColor=green_color,
                                   fillColor=None,
                                   pos=(0, 0))
    
    # 显示测试信息
    msg = visual.TextStim(win, 
                         "瞳孔生物反馈训练程序测试\n\n按空格键开始测试\n按ESC键退出",
                         color=[1, 1, 1],
                         wrapWidth=600)
    msg.draw()
    win.flip()
    
    # 等待按键
    keys = event.waitKeys(keyList=['space', 'escape'])
    if 'escape' in keys:
        win.close()
        core.quit()
        return
    
    # 测试基线阶段显示
    print("测试基线阶段显示...")
    msg = visual.TextStim(win, "基线阶段测试\n注视中央十字", color=[1, 1, 1])
    msg.draw()
    win.flip()
    core.wait(2)
    
    # 显示基线圆圈
    for i in range(100):  # 显示3秒多
        fixation.draw()
        baseline_circle.draw()
        win.flip()
        core.wait(0.03)
    
    # 测试反馈阶段
    print("测试反馈阶段显示...")
    msg = visual.TextStim(win, "反馈阶段测试\n圆圈大小会变化", color=[1, 1, 1])
    msg.draw()
    win.flip()
    core.wait(2)
    
    # 模拟瞳孔大小变化
    base_radius = 50
    for i in range(150):  # 5秒
        # 模拟瞳孔大小变化 (正弦波)
        pupil_ratio = 1.0 + 0.5 * np.sin(i * 0.1)
        new_radius = base_radius * pupil_ratio
        new_line_width = 3 * base_radius / new_radius  # 保持总像素数恒定
        new_line_width = max(1, min(new_line_width, 10))
        
        feedback_circle.radius = new_radius
        feedback_circle.lineWidth = new_line_width
        
        fixation.draw()
        baseline_circle.draw()
        feedback_circle.draw()
        win.flip()
        core.wait(0.033)  # 约30Hz
    
    # 测试成功/失败反馈
    print("测试成功反馈...")
    success_circle = visual.Circle(win,
                                  radius=60,
                                  lineWidth=4,
                                  lineColor=green_color,  # 绿色表示成功
                                  fillColor=None,
                                  pos=(0, 0))
    
    msg = visual.TextStim(win, "成功反馈 (绿色)", color=[1, 1, 1])
    msg.draw()
    win.flip()
    core.wait(1)
    
    fixation.draw()
    success_circle.draw()
    win.flip()
    core.wait(2)
    
    print("测试失败反馈...")
    failure_circle = visual.Circle(win,
                                  radius=40,
                                  lineWidth=5,
                                  lineColor=[green_g/255*2-1, 0, 0],  # 红色表示失败
                                  fillColor=None,
                                  pos=(0, 0))
    
    msg = visual.TextStim(win, "失败反馈 (红色)", color=[1, 1, 1])
    msg.draw()
    win.flip()
    core.wait(1)
    
    fixation.draw()
    failure_circle.draw()
    win.flip()
    core.wait(2)
    
    # 结束测试
    msg = visual.TextStim(win, "测试完成!\n\n按任意键退出", color=[1, 1, 1])
    msg.draw()
    win.flip()
    event.waitKeys()
    
    win.close()
    core.quit()

def test_eyelink_connection():
    """测试EyeLink连接"""
    print("测试EyeLink连接...")
    
    try:
        # 尝试连接EyeLink (模拟模式)
        el_tracker = pylink.EyeLink(None)  # 模拟模式
        print("EyeLink模拟模式连接成功")
        
        # 测试基本命令
        el_tracker.setOfflineMode()
        print("设置离线模式成功")
        
        # 关闭连接
        el_tracker.close()
        print("EyeLink连接测试完成")
        
    except Exception as e:
        print("EyeLink连接测试失败:", e)

def main():
    """主测试函数"""
    print("=" * 50)
    print("瞳孔生物反馈训练程序 - 测试版本")
    print("=" * 50)
    
    # 选择测试项目
    dlg = gui.Dlg("测试选择")
    dlg.addText("请选择要进行的测试:")
    dlg.addField("测试项目:", choices=["基本显示测试", "EyeLink连接测试", "全部测试"])
    
    ok_data = dlg.show()
    if not dlg.OK:
        print("用户取消测试")
        return
    
    test_choice = ok_data["测试项目:"]
    
    if test_choice == "基本显示测试" or test_choice == "全部测试":
        test_basic_setup()
    
    if test_choice == "EyeLink连接测试" or test_choice == "全部测试":
        test_eyelink_connection()
    
    print("所有测试完成!")

if __name__ == '__main__':
    main()
