function <PERSON><PERSON><PERSON><PERSON>
close all;clear all;clc;
[subject, group, session, ntrials] = inputsubinfo_detection;
if ~exist('subject','var')
    subject ='test';
end
if ~exist('group','var')
    group=1;
end
if ~exist('session','var')
    session=1;
end
if ~exist('ntrials','var')
    ntrials=15;
end
%% Prepare stimuli
AssertOpenGL;
whichscreen=max(Screen('Screens'));
Screen('Preference', 'SkipSyncTests', 1)
DotSize=15;
amplifier=0.2;
totaltime = 1;
Hz=30;frame=30;
marker = 15;
markershow=[1:marker];
black=BlackIndex(whichscreen);
white=WhiteIndex(whichscreen);
gray=round((black+white)/2);
[windowPtr, rect]=Screen('OpenWindow',whichscreen,gray,[0 0 1920 1080]);
Screen('BlendFunction', windowPtr, GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
[centerx,centery]=RectCenter(rect);
fps=Screen('NominalFrameRate',windowPtr);
ifi=Screen('GetFlipInterval',windowPtr);
waitframes=2;%round(fps/Hz);
frame=round(fps*totaltime/waitframes);
markershow=[1:marker];%head(1)-shoulders(2/3)-elbows(4/5)-hands(6/7)-waist(8/9)-knees(10/11)-feet(12/15)
%defaultwin=Screen('MakeTexture', windowPtr, imread('Frame.bmp'));
KbName('UnifyKeyNames');
Key1 = KbName('a');
Key2 = KbName('l');
escapeKey = KbName('ESCAPE');
spaceKey = KbName('SPACE');
[touch, secs, keyCode] = KbCheck;
fixLen = 15;
fix = [-fixLen/2,fixLen/2,0,0;0,0,-fixLen/2,fixLen/2];
cueemo=[1:2];% emo sad or happy
cueori=[1:2];% facing left or right
factors=[length(cueemo),length(cueori)];
stimseq=repmat(CombineFactors(factors),ntrials,1);
stimseq=Shuffle(stimseq);
results=zeros(size(stimseq,1),4);
%% Read instruction
instrunum=1;
instrupic = cell(instrunum);
imgname=strcat('intro.png');
[image map alpha] = imread(imgname);
instrupic{1} = image;
instructpicsize=[size(image,2),size(image,1)];
% instructpicrate=1;
%% Show stimuli
HideCursor;
oldPriority=Priority(MaxPriority(windowPtr));
Screen('FillRect', windowPtr,gray,[]);
tex = Screen('MakeTexture',windowPtr,instrupic{1});
Screen('DrawTexture',windowPtr,tex);
vbl = Screen('Flip',windowPtr);
WaitSecs(1);
[touch, secs, keyCode] = KbCheck;
touch = 0;
while ~(touch && (keyCode(spaceKey) || keyCode(escapeKey)))
    [touch, secs, keyCode] = KbCheck;
end
if keyCode(escapeKey)
    Screen('CloseAll');
    errinfo=1;
    return
end
touch = 0;
loadfile=dir('bm_dire/*.txt');
loadfile(1:4)=flipud(loadfile(1:4));
    for i=1:2%两种情绪
        for j=1:2%两个方向
            BMData(:,:,:)=load(strcat('bm_dire/',num2str(i),num2str(j),'.txt'));
            for x=1:frame
                for y=1:marker
                    BMShow(y,1:2,x,(i-1)*2+j)=BMData(x,2*y:(2*y+1),:);
                end
            end
        end
        clear BMData
    end
    amplifier=0.25;
    BMShow=BMShow*amplifier;
    BMShow(:,:,:,1)=BMShow(:,:,:,1)*1.13;
BMShow(:,:,:,2)=BMShow(:,:,:,2)*1.13;
BMShow(:,:,:,3)=BMShow(:,:,:,3)*0.95;
BMShow(:,:,:,4)=BMShow(:,:,:,4)*0.95;
 %% formal

msg=['Press SpaceKey to Start!'];
DrawFormattedText(windowPtr,msg,'center','center',white);
vbl =Screen('Flip',windowPtr);
WaitSecs(0.5);
while ~(touch==1 && (keyCode(spaceKey) || keyCode(escapeKey)))
        [touch, secs, keyCode] = KbCheck;
end
for trial=1:size(stimseq,1)
results(trial,1:2)=stimseq(trial,1:2);
testrect=rect;
 Minx=testrect(4)/900;%upr
[cx,cy]=RectCenter(testrect);
Screen('FillRect', windowPtr,gray,[]);
Screen('Drawlines',windowPtr,fix,2,black,[centerx, centery])
vbl =Screen('Flip',windowPtr);
 WaitSecs(RandSample(800:1200)/1000);
stim_type=stimseq(trial,1)+2*(stimseq(trial,2)-1);
if stim_type==1
    cy1=cy;
elseif stim_type==2
   cy1=cy;
   elseif stim_type==3
   cy1=cy+40;
   elseif stim_type==4
   cy1=cy+40;
end
initframe=RandSample(1:frame);
i=initframe;
Response=0;
trialtime=GetSecs;
showframes=round(fps*2000/1000/waitframes);
for i=1:showframes
    Screen('FillRect', windowPtr,gray);
    Screen('Drawlines',windowPtr,fix,2,black, [centerx, centery]);
    Screen('DrawDots',windowPtr,squeeze(BMShow(:,:,mod(initframe+i-2,frame)+1,stim_type))',DotSize,white,[cx cy1],1);
    vbl = Screen('Flip', windowPtr, vbl + (waitframes-0.5)*ifi);
end
trialtime=GetSecs;
while  Response~=1
    Screen('FillRect', windowPtr,gray,[]);
    %Screen('Drawlines',windowPtr,fix,2,black,[centerx, centery]);
    pic_instru = Screen('MakeTexture', windowPtr, imread('wenhao.png'));
    Screen('DrawTexture',windowPtr,pic_instru,[],[]);
    vbl = Screen('Flip', windowPtr, vbl + (waitframes-0.5)*ifi);
    for waiti=1:30
        [touch, secs, keyCode] = KbCheck;
        if (touch==1 && (keyCode(Key1) || keyCode(Key2) || keyCode(escapeKey)))
            Response=1;
        end
    end
end
if (keyCode(Key1) && stimseq(trial,2)==1) ||(keyCode(Key2) && stimseq(trial,2)==2)
    results(trial,3)=1;
    results(trial,4)=secs-trialtime;
elseif (keyCode(Key2) && stimseq(trial,2)==1) || (keyCode(Key1) && stimseq(trial,2)==2)
    results(trial,3)=0;
    results(trial,4)=secs-trialtime;
elseif keyCode(escapeKey)
    break
end
Screen('Drawlines',windowPtr,fix,2,black, [centerx, centery]);
Screen('Flip',windowPtr);
if mod(trial,20)==0 && trial~=size(stimseq,1)
    msg=['Take a break  ...'];
    DrawFormattedText(windowPtr,msg,'center','center',white);
    Screen('Flip',windowPtr);
    WaitSecs(2);
    msg=['Press Space Key to start  ...'];
    DrawFormattedText(windowPtr,msg,'center','center',white);
    Screen('Flip',windowPtr);
    while ~(touch==1 && (keyCode(spaceKey) || keyCode(escapeKey)))
        [touch, secs, keyCode] = KbCheck;
    end
    if keyCode(escapeKey)
        break
    end
    end
end
Priority(oldPriority);
ShowCursor;
Screen('CloseAll');
datafile=sprintf('Data_Detection\\%s_%s_%s_%s',subject,num2str(session),mfilename,datestr(now,30));
save(datafile,'results','session','stimseq','ntrials');
header1 = [{'direction','emotion', 'response','rt'};...
    num2cell(results)];
sub_expdata_table = cell2table(header1);
exp_data = strcat('Data_Detection\\%s',subject,'RT', '.csv');
writetable(sub_expdata_table, exp_data);
[accs, rts] = f_calc_accrt([datafile,'.mat']);
display(accs);
display(rts);
end