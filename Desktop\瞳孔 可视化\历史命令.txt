查看主目录下fixation_window_fast samples和eyelinkcoregraphics程序框架
查看example/pylink api userguide中的API文档
查看psychopy example/coder中的示例代码
编写瞳孔生物反馈训练程序
创建pupil_biofeedback_training.py主程序文件
创建test_biofeedback.py测试程序文件
复制EyeLinkCoreGraphicsPsychoPy.py到python_code_0709文件夹
创建README.md说明文档
创建config.py配置文件
创建check_config.py配置检查程序
创建run_experiment.bat启动脚本
修改主程序使用外部配置文件
创建使用指南.md详细使用说明
完整阅读fixationWindow_fastSamples程序
参考fixationWindow_fastSamples重构EyeLink连接相关代码
改进校准、消息发送、漂移校正、错误处理等功能
添加阶段提示语功能
在每个阶段前显示可配置的提示语
创建config_example.py展示自定义配置
更新文档说明阶段提示语使用方法
完成瞳孔生物反馈训练程序开发和优化
