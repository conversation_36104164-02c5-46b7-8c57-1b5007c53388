# -*- coding: utf-8 -*-
"""
瞳孔自适应调节阶段调试程序
专门调试自适应调节阶段的显示问题

作者: AI Assistant
日期: 2025-07-09
"""

from __future__ import division
from __future__ import print_function

import os
import sys
import time
import numpy as np
from psychopy import visual, core, event, monitors, gui
from psychopy.hardware import keyboard
from psychopy import logging

# 切换到脚本文件夹
script_path = os.path.dirname(sys.argv[0])
if len(script_path) != 0:
    os.chdir(script_path)

# 只显示关键日志信息
logging.console.setLevel(logging.CRITICAL)

# 导入配置
from config import config

def create_debug_stimuli(win):
    """创建调试用的视觉刺激"""
    stimuli = {}
    
    print("创建视觉刺激...")
    
    # 注视点
    stimuli['fixation'] = visual.TextStim(win, '+', 
                                         height=50, 
                                         color=config.fixation_color,
                                         pos=(0, 0))
    print(f"✓ 注视点创建成功: 颜色={config.fixation_color}")
    
    # 基线虚线圆圈
    stimuli['baseline_circle'] = visual.Circle(win,
                                              radius=config.baseline_circle_radius,
                                              lineWidth=config.circle_line_width,
                                              lineColor=config.baseline_color,
                                              fillColor=None,
                                              pos=(0, 0))
    print(f"✓ 基线圆圈创建成功: 半径={config.baseline_circle_radius}, 颜色={config.baseline_color}")
    
    # 实时反馈圆圈
    stimuli['feedback_circle'] = visual.Circle(win,
                                              radius=config.baseline_circle_radius,
                                              lineWidth=config.circle_line_width,
                                              lineColor=config.feedback_color,
                                              fillColor=None,
                                              pos=(0, 0))
    print(f"✓ 反馈圆圈创建成功: 半径={config.baseline_circle_radius}, 颜色={config.feedback_color}")
    
    return stimuli

def calculate_circle_properties_debug(pupil_size, baseline_size, baseline_radius):
    """计算圆圈属性（调试版本）"""
    print(f"计算圆圈属性: 瞳孔大小={pupil_size:.3f}, 基线大小={baseline_size:.3f}")
    
    if baseline_size is None or baseline_size <= 0:
        print("⚠️ 基线大小无效，使用默认值")
        return baseline_radius, config.circle_line_width
    
    # 计算半径比例
    size_ratio = pupil_size / baseline_size
    new_radius = baseline_radius * size_ratio
    
    # 计算线宽以保持总像素数恒定
    new_line_width = config.circle_line_width * baseline_radius / new_radius
    
    # 限制线宽范围
    new_line_width = max(1, min(new_line_width, 20))
    
    print(f"  → 新半径={new_radius:.1f}, 新线宽={new_line_width:.1f}")
    
    return new_radius, new_line_width

def debug_modulation_phase():
    """调试自适应调节阶段"""
    print("=" * 60)
    print("瞳孔自适应调节阶段调试程序")
    print("=" * 60)
    
    # 创建窗口
    print("创建PsychoPy窗口...")
    try:
        win = visual.Window(size=(800, 600),
                            fullscr=False,
                            units='pix',
                            color=config.bg_color)
        print(f"✓ 窗口创建成功: 背景颜色={config.bg_color}")
    except Exception as e:
        print(f"❌ 窗口创建失败: {e}")
        return False
    
    # 创建视觉刺激
    stimuli = create_debug_stimuli(win)
    
    # 模拟基线瞳孔大小
    baseline_size = 4.0  # mm
    print(f"模拟基线瞳孔大小: {baseline_size} mm")
    
    # 创建键盘对象
    kb = keyboard.Keyboard()
    
    # 显示说明
    instruction = visual.TextStim(win, 
                                 ("瞳孔自适应调节阶段调试\n\n"
                                  "将模拟瞳孔大小变化\n"
                                  "应该看到:\n"
                                  "• 白色注视点 (+)\n"
                                  "• 绿色基线圆圈 (固定大小)\n"
                                  "• 绿色反馈圆圈 (变化大小)\n\n"
                                  "按空格键开始测试\n"
                                  "按ESC键退出"),
                                 color=[1, 1, 1],
                                 height=25,
                                 wrapWidth=600)
    
    instruction.draw()
    win.flip()
    
    # 等待开始
    keys = event.waitKeys(keyList=['space', 'escape'])
    if 'escape' in keys:
        win.close()
        return False
    
    print("\n开始调试自适应调节阶段...")
    
    # 测试1: 静态显示
    print("\n测试1: 静态显示所有元素")
    
    # 清空屏幕
    win.flip()
    
    # 绘制所有元素
    stimuli['fixation'].draw()
    stimuli['baseline_circle'].draw()
    stimuli['feedback_circle'].draw()
    win.flip()
    
    print("显示静态元素3秒...")
    core.wait(3.0)
    
    # 检查是否能看到元素
    check_msg = visual.TextStim(win, 
                               "您能看到注视点和两个绿色圆圈吗?\n\n"
                               "Y - 能看到\n"
                               "N - 看不到\n"
                               "ESC - 退出",
                               color=[1, 1, 1],
                               height=30)
    check_msg.draw()
    win.flip()
    
    keys = event.waitKeys(keyList=['y', 'n', 'escape'])
    if 'escape' in keys:
        win.close()
        return False
    elif 'n' in keys:
        print("❌ 静态显示失败 - 检查颜色和大小设置")
        # 显示调试信息
        debug_info = (f"调试信息:\n"
                     f"背景颜色: {config.bg_color}\n"
                     f"注视点颜色: {config.fixation_color}\n"
                     f"基线圆圈颜色: {config.baseline_color}\n"
                     f"反馈圆圈颜色: {config.feedback_color}\n"
                     f"圆圈半径: {config.baseline_circle_radius}\n"
                     f"线宽: {config.circle_line_width}")
        
        debug_msg = visual.TextStim(win, debug_info, color=[1, 1, 1], height=20)
        debug_msg.draw()
        win.flip()
        event.waitKeys()
        win.close()
        return False
    else:
        print("✓ 静态显示正常")
    
    # 测试2: 动态变化
    print("\n测试2: 模拟瞳孔大小动态变化")
    
    start_time = time.time()
    test_duration = 10.0  # 测试10秒
    
    print(f"开始{test_duration}秒的动态测试...")
    
    frame_count = 0
    last_update_time = start_time
    
    while time.time() - start_time < test_duration:
        current_time = time.time()
        
        # 检查键盘
        keys = kb.getKeys(['escape'])
        if keys:
            break
        
        # 模拟瞳孔大小变化（正弦波）
        elapsed = current_time - start_time
        pupil_variation = 0.5 * np.sin(elapsed * 2)  # 2 Hz变化
        simulated_pupil_size = baseline_size + pupil_variation
        
        # 每30Hz更新一次（模拟实际反馈频率）
        if current_time - last_update_time >= 1.0/30.0:
            last_update_time = current_time
            frame_count += 1
            
            # 计算新的圆圈属性
            new_radius, new_line_width = calculate_circle_properties_debug(
                simulated_pupil_size, baseline_size, config.baseline_circle_radius)
            
            # 更新反馈圆圈
            stimuli['feedback_circle'].radius = new_radius
            stimuli['feedback_circle'].lineWidth = new_line_width
            
            # 绘制所有元素
            stimuli['fixation'].draw()
            stimuli['baseline_circle'].draw()
            stimuli['feedback_circle'].draw()
            
            # 添加调试信息
            debug_text = (f"帧数: {frame_count}\n"
                         f"模拟瞳孔: {simulated_pupil_size:.2f}mm\n"
                         f"基线大小: {baseline_size:.2f}mm\n"
                         f"反馈半径: {new_radius:.1f}px\n"
                         f"反馈线宽: {new_line_width:.1f}px")
            
            info_display = visual.TextStim(win, debug_text,
                                          pos=(-300, 200),
                                          color=[1, 1, 1],
                                          height=20,
                                          anchorHoriz='left')
            info_display.draw()
            
            win.flip()
            
            if frame_count % 30 == 0:  # 每秒打印一次
                print(f"  帧{frame_count}: 瞳孔={simulated_pupil_size:.2f}mm, 半径={new_radius:.1f}px")
    
    print(f"✓ 动态测试完成，共更新{frame_count}帧")
    
    # 测试3: 极端值测试
    print("\n测试3: 极端瞳孔大小测试")
    
    extreme_values = [1.0, 2.0, 3.0, 5.0, 7.0, 9.0]  # 不同的瞳孔大小
    
    for pupil_size in extreme_values:
        print(f"测试瞳孔大小: {pupil_size}mm")
        
        new_radius, new_line_width = calculate_circle_properties_debug(
            pupil_size, baseline_size, config.baseline_circle_radius)
        
        stimuli['feedback_circle'].radius = new_radius
        stimuli['feedback_circle'].lineWidth = new_line_width
        
        # 显示
        stimuli['fixation'].draw()
        stimuli['baseline_circle'].draw()
        stimuli['feedback_circle'].draw()
        
        size_info = visual.TextStim(win, 
                                   f"瞳孔大小: {pupil_size}mm\n"
                                   f"圆圈半径: {new_radius:.1f}px\n"
                                   f"线宽: {new_line_width:.1f}px",
                                   pos=(0, -200),
                                   color=[1, 1, 1],
                                   height=25)
        size_info.draw()
        win.flip()
        
        core.wait(1.5)
    
    # 最终检查
    final_msg = visual.TextStim(win, 
                               ("调试测试完成!\n\n"
                                "您看到了:\n"
                                "1. 静态显示的圆圈?\n"
                                "2. 动态变化的反馈圆圈?\n"
                                "3. 不同大小的极端值测试?\n\n"
                                "如果都能看到，说明显示功能正常\n"
                                "如果看不到，请检查颜色设置\n\n"
                                "按任意键退出"),
                               color=[1, 1, 1],
                               height=25)
    final_msg.draw()
    win.flip()
    event.waitKeys()
    
    win.close()
    print("✓ 调试程序完成")
    return True

def check_color_contrast():
    """检查颜色对比度"""
    print("\n检查颜色设置和对比度:")
    print(f"背景颜色: {config.bg_color}")
    print(f"注视点颜色: {config.fixation_color}")
    print(f"基线圆圈颜色: {config.baseline_color}")
    print(f"反馈圆圈颜色: {config.feedback_color}")

    # 计算颜色对比度
    def color_distance(c1, c2):
        return sum((a - b) ** 2 for a, b in zip(c1, c2)) ** 0.5

    bg_baseline_dist = color_distance(config.bg_color, config.baseline_color)
    bg_feedback_dist = color_distance(config.bg_color, config.feedback_color)

    print(f"背景与基线圆圈颜色距离: {bg_baseline_dist:.3f}")
    print(f"背景与反馈圆圈颜色距离: {bg_feedback_dist:.3f}")

    if bg_baseline_dist < 0.5:
        print("⚠️ 警告: 背景与基线圆圈颜色太接近，可能看不清")
    if bg_feedback_dist < 0.5:
        print("⚠️ 警告: 背景与反馈圆圈颜色太接近，可能看不清")

def test_main_program_integration():
    """测试与主程序的集成"""
    print("\n测试主程序集成:")

    try:
        # 测试导入主程序模块
        import pupil_biofeedback_training as main_prog
        print("✓ 主程序模块导入成功")

        # 测试创建视觉刺激函数
        from psychopy import visual, monitors

        win = visual.Window(size=(400, 300), fullscr=False, units='pix', color=config.bg_color)
        stimuli = main_prog.create_visual_stimuli(win)
        print("✓ 主程序视觉刺激创建成功")

        # 测试圆圈属性计算
        new_radius, new_line_width = main_prog.calculate_circle_properties(4.0, 3.5, 50)
        print(f"✓ 圆圈属性计算成功: 半径={new_radius:.1f}, 线宽={new_line_width:.1f}")

        win.close()
        return True

    except Exception as e:
        print(f"❌ 主程序集成测试失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 检查颜色设置
        check_color_contrast()

        # 测试主程序集成
        integration_ok = test_main_program_integration()

        # 运行显示测试
        success = debug_modulation_phase()

        if success and integration_ok:
            print("\n" + "=" * 60)
            print("✓ 所有调试测试通过!")
            print("\n调试建议:")
            print("1. 如果主程序中仍然看不到圆圈，可能是EyeLink连接问题")
            print("2. 检查config.py中的dummy_mode设置")
            print("3. 确保瞳孔数据处理器正常工作")
            print("4. 检查反馈更新频率设置")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ 部分测试失败!")
            print("\n问题排查:")
            print("1. 检查config.py中的颜色设置")
            print("2. 调整baseline_circle_radius和circle_line_width")
            print("3. 确保PsychoPy正常工作")
            print("4. 检查字体和显示设置")
            print("=" * 60)

    except Exception as e:
        print(f"调试程序出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
