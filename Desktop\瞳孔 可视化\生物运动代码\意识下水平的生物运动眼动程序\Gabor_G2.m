

function     [Grating]=Gabor_G2(GaussTrigger,Gdiameter,Gangle,Gfrequency,alpha1,BackgroundColor,Ramp,Gamp,sigma,contrast)


Rbase=BackgroundColor(1);
Gbase=BackgroundColor(2);


BackgroundColor0=0.5; % 0.5 is gray

[x,y]=meshgrid(1:Gdiameter,1:Gdiameter);
outrng=(x-(Gdiameter+1)/2).^2+(y-(Gdiameter+1)/2).^2>=((Gdiameter+1)./2).^2;

patch0 = cos(2*pi*Gfrequency/Gdiameter*(x*sin(Gangle)+y*cos(Gangle)))*contrast;  % JY


gaussianfilter = fspecial('gaussian', Gdiameter, sigma);
gaussianfilter = gaussianfilter/max(gaussianfilter(:));
% BackgroundColor=BackgroundColor./255;
patch = uint8((gaussianfilter.*patch0+ BackgroundColor0)*255);   % JiangYi
% figure;
% subplot(1,2,1);imshow(patch0);
% subplot(1,2,2);imshow(patch);


if Ramp<=0;     Ramp=0.1;   end
if Gamp<=0;     Gamp=0.1;   end
% BackgroundColor=[134 151 0]./255;
% BackgroundColor=Rbase/255+Gbase/255;
% [~,patch0]=gaborpatch_Contrast(Gdiameter, angles(agl), Gfrequency, sigma, 0.5,gray);
if GaussTrigger==1;PatchNow=patch;elseif GaussTrigger==0;PatchNow=patch0;end
Rplate=scaleif(double(PatchNow),Rbase-Ramp,Rbase+Ramp);
Rplate_anti=scaleif(double(255-PatchNow),Rbase-Ramp,Rbase+Ramp);
Gplate=scaleif(double(255-PatchNow),Gbase-Gamp,Gbase+Gamp);
Gplate_anti=scaleif(double(PatchNow),Gbase-Gamp,Gbase+Gamp);
Bplate=zeros(size(Gplate));
% imshow(outrng)
% Rplate(outrng)=BackgroundColor;
% Rplate_anti(outrng)=BackgroundColor;
% Gplate(outrng)=BackgroundColor;
% Gplate_anti(outrng)=BackgroundColor;
% Bplate(outrng)=BackgroundColor;
Alphaplate=ones(size(Gplate)).*alpha1;
Alphaplate(outrng)=0;
RGplate(:,:,1)=Rplate;  RGplate(:,:,2)=Gplate;  RGplate(:,:,3)=Bplate;  RGplate(:,:,4)=Alphaplate;
RGplate_anti(:,:,1)=Rplate_anti;  RGplate_anti(:,:,2)=Gplate_anti;  RGplate_anti(:,:,3)=Bplate;
RGplate_anti(:,:,4)=Alphaplate;
Grating{1}=RGplate;
Grating{2}=RGplate_anti;
% figure;
% subplot(1,2,1)
% imshow(RGplate(:,:,1:3)/255);
% subplot(1,2,2)
% imshow(RGplate_anti(:,:,1:3)/255);
return
