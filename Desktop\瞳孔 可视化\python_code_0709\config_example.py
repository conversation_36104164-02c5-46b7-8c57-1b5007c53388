# -*- coding: utf-8 -*-
"""
瞳孔生物反馈训练程序配置文件示例
展示如何自定义阶段提示语和时间设置

作者: AI Assistant
日期: 2025-07-09
"""

from config import ExperimentConfig

class CustomExperimentConfig(ExperimentConfig):
    """自定义实验配置类示例"""
    
    def __init__(self):
        super().__init__()
        
        # ==================== 自定义阶段提示语时间 ====================
        self.phase_prompt_duration = 3.0   # 阶段提示语显示时长（秒）
        self.show_phase_prompts = True     # 是否显示阶段提示语
        
        # ==================== 自定义阶段提示语文本 ====================
        # 基线阶段提示语
        self.baseline_prompt = "准备开始基线测量\n\n请注视屏幕中央的十字\n保持自然状态"
        
        # 自主调适阶段提示语（放大条件）
        self.modulation_prompt_enlarge = ("自主调适阶段\n\n"
                                         "请尝试放大瞳孔\n"
                                         "可以想象明亮的场景\n"
                                         "或者感到兴奋的事情")
        
        # 自主调适阶段提示语（缩小条件）
        self.modulation_prompt_shrink = ("自主调适阶段\n\n"
                                        "请尝试缩小瞳孔\n"
                                        "可以想象昏暗的环境\n"
                                        "或者保持放松状态")
        
        # 反馈阶段提示语
        self.feedback_prompt = "反馈阶段\n\n即将显示您的表现结果"
        
        # ==================== 其他自定义设置示例 ====================
        # 可以根据需要调整其他参数
        self.baseline_duration = 8.0       # 延长基线阶段到8秒
        self.modulation_duration = 20.0    # 延长自主调适阶段到20秒
        self.feedback_duration = 3.0       # 延长反馈阶段到3秒
        
        # 字体设置
        self.font_size = 35                # 增大字体
        self.text_wrap_width = 800         # 增加文本宽度

# 使用示例：
# 1. 将此文件重命名为 config.py 来替换默认配置
# 2. 或者在主程序中导入：from config_example import CustomExperimentConfig as config

# 不同实验条件的配置示例
class ShortPromptConfig(ExperimentConfig):
    """短提示语配置"""
    def __init__(self):
        super().__init__()
        self.phase_prompt_duration = 1.5
        self.baseline_prompt = "基线阶段"
        self.modulation_prompt_enlarge = "放大瞳孔"
        self.modulation_prompt_shrink = "缩小瞳孔"
        self.feedback_prompt = "查看结果"

class DetailedPromptConfig(ExperimentConfig):
    """详细提示语配置"""
    def __init__(self):
        super().__init__()
        self.phase_prompt_duration = 4.0
        self.baseline_prompt = ("基线测量阶段\n\n"
                               "请保持自然状态\n"
                               "注视屏幕中央的十字\n"
                               "不要刻意控制瞳孔大小")
        
        self.modulation_prompt_enlarge = ("瞳孔放大训练\n\n"
                                         "请尝试以下方法放大瞳孔：\n"
                                         "• 想象明亮的阳光\n"
                                         "• 回忆兴奋的时刻\n"
                                         "• 保持积极的情绪状态")
        
        self.modulation_prompt_shrink = ("瞳孔缩小训练\n\n"
                                        "请尝试以下方法缩小瞳孔：\n"
                                        "• 想象昏暗的房间\n"
                                        "• 保持平静放松\n"
                                        "• 进行深呼吸")
        
        self.feedback_prompt = ("表现反馈\n\n"
                               "即将显示您在本次试验中的表现\n"
                               "绿色表示成功，红色表示需要改进")

class NoPromptConfig(ExperimentConfig):
    """无提示语配置"""
    def __init__(self):
        super().__init__()
        self.show_phase_prompts = False  # 关闭阶段提示语

# 配置使用说明
"""
要使用自定义配置，有以下几种方法：

方法1：直接修改 config.py 文件中的参数值

方法2：创建自定义配置类并替换默认配置
```python
# 在主程序开头添加：
from config_example import CustomExperimentConfig
config = CustomExperimentConfig()
```

方法3：运行时动态修改配置
```python
from config import config
config.phase_prompt_duration = 3.0
config.baseline_prompt = "自定义基线提示语"
```

参数说明：
- phase_prompt_duration: 阶段提示语显示时长（秒）
- show_phase_prompts: 是否显示阶段提示语（True/False）
- baseline_prompt: 基线阶段提示语文本
- modulation_prompt_enlarge: 放大训练提示语文本
- modulation_prompt_shrink: 缩小训练提示语文本
- feedback_prompt: 反馈阶段提示语文本

注意事项：
1. 提示语文本支持换行符 \n
2. 提示语时长建议在1-5秒之间
3. 可以根据被试群体调整提示语的详细程度
4. 如果不需要某个阶段的提示语，可以设置为空字符串 ""
"""
