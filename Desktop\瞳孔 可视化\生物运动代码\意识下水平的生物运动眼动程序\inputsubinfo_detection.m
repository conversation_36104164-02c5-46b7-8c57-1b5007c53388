function [subject, group, session, ntrials] = inputsubinfo_detection
prompt={'Enter subject number:','Enter group(ASD=1;TD=2):','Enter session(1;2;3):','Enter trial number:'};
name='Experimental Information';
numlines=1;
defaultanswer={'xxx','1','1','5'};
answer=inputdlg(prompt,name,numlines,defaultanswer);
subject=answer{1};
group=str2double(answer{2});
session=str2double(answer{3});
% time_gap=str2double(answer{4});
ntrials=str2double(answer{4});
% BMemo=str2double(answer{6});
return