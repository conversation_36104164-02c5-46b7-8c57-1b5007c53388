# -*- coding: utf-8 -*-
"""
配置检查程序
验证实验配置是否正确

作者: AI Assistant
日期: 2025-07-09
"""

import sys
import os

def check_config():
    """检查配置文件"""
    print("=" * 60)
    print("瞳孔生物反馈训练程序 - 配置检查")
    print("=" * 60)
    
    try:
        # 导入配置
        from config import config
        print("✓ 配置文件导入成功")
        
        # 显示配置信息
        config.print_config()
        
        # 验证配置
        errors = config.validate_config()
        if errors:
            print("\n❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        else:
            print("\n✓ 配置验证通过!")
        
        # 检查依赖库
        print("\n检查依赖库:")
        
        try:
            import psychopy
            print(f"✓ PsychoPy {psychopy.__version__}")
        except ImportError:
            print("❌ PsychoPy 未安装")
            return False
        
        try:
            import pylink
            print("✓ PyLink 已安装")
        except ImportError:
            print("❌ PyLink 未安装")
            return False
        
        try:
            import numpy
            print(f"✓ NumPy {numpy.__version__}")
        except ImportError:
            print("❌ NumPy 未安装")
            return False
        
        try:
            from PIL import Image
            print("✓ PIL/Pillow 已安装")
        except ImportError:
            print("❌ PIL/Pillow 未安装")
            return False
        
        # 检查文件
        print("\n检查必要文件:")
        
        required_files = [
            'pupil_biofeedback_training.py',
            'EyeLinkCoreGraphicsPsychoPy.py',
            'config.py'
        ]
        
        for file in required_files:
            if os.path.exists(file):
                print(f"✓ {file}")
            else:
                print(f"❌ {file} 缺失")
                return False
        
        # 检查结果文件夹
        if not os.path.exists(config.results_folder):
            try:
                os.makedirs(config.results_folder)
                print(f"✓ 创建结果文件夹: {config.results_folder}")
            except Exception as e:
                print(f"❌ 无法创建结果文件夹: {e}")
                return False
        else:
            print(f"✓ 结果文件夹存在: {config.results_folder}")
        
        print("\n" + "=" * 60)
        print("✓ 所有检查通过! 程序可以正常运行。")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"\n❌ 配置检查失败: {e}")
        return False

def main():
    """主函数"""
    success = check_config()
    
    if success:
        print("\n建议:")
        print("1. 运行 test_biofeedback.py 进行基本功能测试")
        print("2. 确保EyeLink眼动仪已连接并正常工作")
        print("3. 运行 pupil_biofeedback_training.py 开始实验")
    else:
        print("\n请修复上述问题后重新运行检查。")
    
    input("\n按Enter键退出...")

if __name__ == '__main__':
    main()
