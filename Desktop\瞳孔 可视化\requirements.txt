# EyeLink瞳孔直径实时显示程序依赖包
# Requirements for EyeLink Pupil Diameter Real-time Display

# 核心科学计算库
numpy>=1.21.0
matplotlib>=3.5.0

# GUI库 (tkinter通常随Python安装，但某些环境可能需要单独安装)
# tkinter  # 通常不需要pip安装

# EyeLink Python API (需要从SR Research安装)
# 注意: 这个包需要从SR Research的PyPI服务器安装
# 安装命令: pip install --index-url=https://pypi.sr-research.com sr-research-pylink
sr-research-pylink>=2.1.0

# 可选: 如果需要更高级的数据处理
scipy>=1.7.0
pandas>=1.3.0

# 可选: 如果需要保存数据到文件
# h5py>=3.0.0  # 用于HDF5格式
# openpyxl>=3.0.0  # 用于Excel格式
